2025-06-11 15:51:50,372 - train - INFO - Starting training with config: {'MODEL': {'NUM_JOINTS': 17, 'IMAGE_SIZE': [256, 256], 'FC_DIM': [1024, 512], 'BACKBONE_LAYERS': 50, 'BACKBONE_PRETRAINED': True, 'SKELETON': [[15, 13], [13, 11], [16, 14], [14, 12], [11, 12], [5, 11], [6, 12], [5, 6], [5, 7], [6, 8], [7, 9], [8, 10], [1, 2], [0, 1], [0, 2], [1, 3], [2, 4], [3, 5], [4, 6]]}, 'DATASET': {'ROOT': './datasets/PoseTrack21/data', 'TRAIN_SET': 'train', 'VAL_SET': 'val', 'TEST_SET': 'test', 'FLIP': True, 'SCALE_FACTOR': 0.3, 'ROT_FACTOR': 40, 'COLOR_RGB': True}, 'TRAIN': {'BATCH_SIZE': 8, 'NUM_WORKERS': 4, 'EPOCHS': 20, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'LR_SCHEDULE': 'cosine', 'WARMUP_EPOCHS': 5, 'KEYPOINT_WEIGHT': 1.0, 'SPATIAL_WEIGHT': 1.0, 'TEMPORAL_WEIGHT': 1.0}, 'VAL': {'BATCH_SIZE': 32, 'FLIP_TEST': True}, 'INFERENCE': {'NMS_THRESHOLD': 0.5, 'KEYPOINT_THRESHOLD': 0.3, 'SPATIAL_THRESHOLD': 0.3, 'TEMPORAL_THRESHOLD': 0.3}, 'TRACKING': {'MAX_DISTANCE': 50, 'MIN_CONFIDENCE': 0.3, 'MAX_MISSING_FRAMES': 5}, 'OUTPUT': {'DIR': './outputs/complete_training/posetrack_20250611_155150', 'SAVE_FREQ': 5, 'EVAL_FREQ': 2, 'VIS_FREQ': 1}, 'LOG': {'LEVEL': 'INFO', 'SAVE_DIR': './logs'}, 'DEVICE': 'cuda', 'MIXED_PRECISION': True, 'SEED': 42}
2025-06-11 15:51:50,373 - train - INFO - Creating data loaders...
2025-06-11 15:51:53,126 - train - INFO - Training dataset: {'total_samples': 118396, 'total_videos': 593, 'total_annotations': 118396, 'total_tracks': 36, 'avg_frames_per_video': np.float64(199.65598650927487), 'max_frames_per_video': np.int64(837), 'min_frames_per_video': np.int64(33)}
2025-06-11 15:51:53,126 - train - INFO - Validation dataset: {'total_samples': 58489, 'total_videos': 170, 'total_annotations': 58489, 'total_tracks': 80, 'avg_frames_per_video': np.float64(344.0529411764706), 'max_frames_per_video': np.int64(1441), 'min_frames_per_video': np.int64(55)}
2025-06-11 15:51:53,126 - train - INFO - Creating model and optimizer...
2025-06-11 15:51:54,044 - train - INFO - Starting training...
2025-06-11 15:51:54,816 - train - INFO - Epoch 0, Batch 0/14799, Loss: 27.9421
2025-06-11 15:51:59,601 - train - INFO - Epoch 0, Batch 100/14799, Loss: 4.2056
2025-06-11 15:52:04,454 - train - INFO - Epoch 0, Batch 200/14799, Loss: -3.5855
2025-06-11 15:52:09,341 - train - INFO - Epoch 0, Batch 300/14799, Loss: -6.4299
2025-06-11 15:52:14,184 - train - INFO - Epoch 0, Batch 400/14799, Loss: -5.6385
2025-06-11 15:52:19,027 - train - INFO - Epoch 0, Batch 500/14799, Loss: -7.7534
2025-06-11 15:52:23,852 - train - INFO - Epoch 0, Batch 600/14799, Loss: -8.6249
2025-06-11 15:52:28,856 - train - INFO - Epoch 0, Batch 700/14799, Loss: -7.8143
2025-06-11 15:52:33,697 - train - INFO - Epoch 0, Batch 800/14799, Loss: -8.3446
