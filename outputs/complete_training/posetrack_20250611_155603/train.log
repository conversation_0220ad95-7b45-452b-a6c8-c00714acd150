2025-06-11 15:56:03,179 - train - INFO - Starting training with config: {'MODEL': {'NUM_JOINTS': 17, 'IMAGE_SIZE': [256, 256], 'FC_DIM': [1024, 512], 'BACKBONE_LAYERS': 50, 'BACKBONE_PRETRAINED': True, 'SKELETON': [[15, 13], [13, 11], [16, 14], [14, 12], [11, 12], [5, 11], [6, 12], [5, 6], [5, 7], [6, 8], [7, 9], [8, 10], [1, 2], [0, 1], [0, 2], [1, 3], [2, 4], [3, 5], [4, 6]]}, 'DATASET': {'ROOT': './datasets/PoseTrack21/data', 'TRAIN_SET': 'train', 'VAL_SET': 'val', 'TEST_SET': 'test', 'FLIP': True, 'SCALE_FACTOR': 0.3, 'ROT_FACTOR': 40, 'COLOR_RGB': True}, 'TRAIN': {'BATCH_SIZE': 8, 'NUM_WORKERS': 4, 'EPOCHS': 20, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'LR_SCHEDULE': 'cosine', 'WARMUP_EPOCHS': 5, 'KEYPOINT_WEIGHT': 1.0, 'SPATIAL_WEIGHT': 1.0, 'TEMPORAL_WEIGHT': 1.0}, 'VAL': {'BATCH_SIZE': 32, 'FLIP_TEST': True}, 'INFERENCE': {'NMS_THRESHOLD': 0.5, 'KEYPOINT_THRESHOLD': 0.3, 'SPATIAL_THRESHOLD': 0.3, 'TEMPORAL_THRESHOLD': 0.3}, 'TRACKING': {'MAX_DISTANCE': 50, 'MIN_CONFIDENCE': 0.3, 'MAX_MISSING_FRAMES': 5}, 'OUTPUT': {'DIR': './outputs/complete_training/posetrack_20250611_155603', 'SAVE_FREQ': 5, 'EVAL_FREQ': 2, 'VIS_FREQ': 1}, 'LOG': {'LEVEL': 'INFO', 'SAVE_DIR': './logs'}, 'DEVICE': 'cuda', 'MIXED_PRECISION': True, 'SEED': 42}
2025-06-11 15:56:03,180 - train - INFO - Creating data loaders...
2025-06-11 15:56:05,956 - train - INFO - Training dataset: {'total_samples': 118396, 'total_videos': 593, 'total_annotations': 118396, 'total_tracks': 36, 'avg_frames_per_video': np.float64(199.65598650927487), 'max_frames_per_video': np.int64(837), 'min_frames_per_video': np.int64(33)}
2025-06-11 15:56:05,956 - train - INFO - Validation dataset: {'total_samples': 58489, 'total_videos': 170, 'total_annotations': 58489, 'total_tracks': 80, 'avg_frames_per_video': np.float64(344.0529411764706), 'max_frames_per_video': np.int64(1441), 'min_frames_per_video': np.int64(55)}
2025-06-11 15:56:05,956 - train - INFO - Creating model and optimizer...
2025-06-11 15:56:06,835 - train - INFO - Starting training...
2025-06-11 15:56:07,515 - train - INFO - Epoch 0, Batch 0/14799, Loss: 42.6186
2025-06-11 15:56:11,349 - train - INFO - Epoch 0, Batch 100/14799, Loss: 21.7664
2025-06-11 15:56:15,203 - train - INFO - Epoch 0, Batch 200/14799, Loss: 19.1109
2025-06-11 15:56:19,199 - train - INFO - Epoch 0, Batch 300/14799, Loss: 33.0142
2025-06-11 15:56:23,267 - train - INFO - Epoch 0, Batch 400/14799, Loss: 24.5956
2025-06-11 15:56:27,119 - train - INFO - Epoch 0, Batch 500/14799, Loss: 13.2448
2025-06-11 15:56:27,119 - train - INFO - Generating visualization at step 500...
2025-06-11 15:56:28,030 - train - INFO - Visualization saved: ./outputs/complete_training/posetrack_20250611_155603/visualizations/epoch_000_step_00500.png
2025-06-11 15:56:31,871 - train - INFO - Epoch 0, Batch 600/14799, Loss: 27.9804
2025-06-11 15:56:35,787 - train - INFO - Epoch 0, Batch 700/14799, Loss: 27.9330
2025-06-11 15:56:39,769 - train - INFO - Epoch 0, Batch 800/14799, Loss: 16.1606
2025-06-11 15:56:43,579 - train - INFO - Epoch 0, Batch 900/14799, Loss: 17.3579
2025-06-11 15:56:47,569 - train - INFO - Epoch 0, Batch 1000/14799, Loss: 18.2784
2025-06-11 15:56:47,569 - train - INFO - Generating visualization at step 1000...
2025-06-11 15:56:48,404 - train - INFO - Visualization saved: ./outputs/complete_training/posetrack_20250611_155603/visualizations/epoch_000_step_01000.png
2025-06-11 15:56:52,255 - train - INFO - Epoch 0, Batch 1100/14799, Loss: 15.5334
2025-06-11 15:56:56,116 - train - INFO - Epoch 0, Batch 1200/14799, Loss: 18.9859
2025-06-11 15:57:00,046 - train - INFO - Epoch 0, Batch 1300/14799, Loss: 23.7435
2025-06-11 15:57:03,863 - train - INFO - Epoch 0, Batch 1400/14799, Loss: 15.4863
2025-06-11 15:57:07,762 - train - INFO - Epoch 0, Batch 1500/14799, Loss: 21.4228
2025-06-11 15:57:07,762 - train - INFO - Generating visualization at step 1500...
2025-06-11 15:57:08,504 - train - INFO - Visualization saved: ./outputs/complete_training/posetrack_20250611_155603/visualizations/epoch_000_step_01500.png
2025-06-11 15:57:12,325 - train - INFO - Epoch 0, Batch 1600/14799, Loss: 30.6436
2025-06-11 15:57:16,205 - train - INFO - Epoch 0, Batch 1700/14799, Loss: 23.9873
2025-06-11 15:57:20,139 - train - INFO - Epoch 0, Batch 1800/14799, Loss: 10.7149
2025-06-11 15:57:23,950 - train - INFO - Epoch 0, Batch 1900/14799, Loss: 26.5167
2025-06-11 15:57:28,193 - train - INFO - Epoch 0, Batch 2000/14799, Loss: 37.9033
2025-06-11 15:57:28,193 - train - INFO - Generating visualization at step 2000...
2025-06-11 15:57:29,087 - train - INFO - Visualization saved: ./outputs/complete_training/posetrack_20250611_155603/visualizations/epoch_000_step_02000.png
2025-06-11 15:57:32,944 - train - INFO - Epoch 0, Batch 2100/14799, Loss: 13.8002
2025-06-11 15:57:36,800 - train - INFO - Epoch 0, Batch 2200/14799, Loss: 19.9590
2025-06-11 15:57:40,680 - train - INFO - Epoch 0, Batch 2300/14799, Loss: 26.6029
2025-06-11 15:57:44,525 - train - INFO - Epoch 0, Batch 2400/14799, Loss: 14.1840
2025-06-11 15:57:48,405 - train - INFO - Epoch 0, Batch 2500/14799, Loss: 18.4447
2025-06-11 15:57:48,405 - train - INFO - Generating visualization at step 2500...
2025-06-11 15:57:49,088 - train - INFO - Visualization saved: ./outputs/complete_training/posetrack_20250611_155603/visualizations/epoch_000_step_02500.png
2025-06-11 15:57:52,999 - train - INFO - Epoch 0, Batch 2600/14799, Loss: 20.6395
2025-06-11 15:57:56,773 - train - INFO - Epoch 0, Batch 2700/14799, Loss: 12.3486
