2025-06-11 16:23:48,376 - train - INFO - Starting training with config: {'MODEL': {'NUM_JOINTS': 17, 'IMAGE_SIZE': [256, 256], 'FC_DIM': [1024, 512], 'BACKBONE_LAYERS': 50, 'BACKBONE_PRETRAINED': True, 'SKELETON': [[15, 13], [13, 11], [16, 14], [14, 12], [11, 12], [5, 11], [6, 12], [5, 6], [5, 7], [6, 8], [7, 9], [8, 10], [1, 2], [0, 1], [0, 2], [1, 3], [2, 4], [3, 5], [4, 6]]}, 'DATASET': {'ROOT': './datasets/PoseTrack21/data', 'TRAIN_SET': 'train', 'VAL_SET': 'val', 'TEST_SET': 'test', 'FLIP': True, 'SCALE_FACTOR': 0.3, 'ROT_FACTOR': 40, 'COLOR_RGB': True}, 'TRAIN': {'BATCH_SIZE': 8, 'NUM_WORKERS': 8, 'EPOCHS': 1, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'LR_SCHEDULE': 'cosine', 'WARMUP_EPOCHS': 5, 'KEYPOINT_WEIGHT': 1.0, 'SPATIAL_WEIGHT': 1.0, 'TEMPORAL_WEIGHT': 1.0}, 'VAL': {'BATCH_SIZE': 32, 'FLIP_TEST': True}, 'INFERENCE': {'NMS_THRESHOLD': 0.5, 'KEYPOINT_THRESHOLD': 0.3, 'SPATIAL_THRESHOLD': 0.3, 'TEMPORAL_THRESHOLD': 0.3}, 'TRACKING': {'MAX_DISTANCE': 50, 'MIN_CONFIDENCE': 0.3, 'MAX_MISSING_FRAMES': 5}, 'OUTPUT': {'DIR': './outputs/posetrack_20250611_162348', 'SAVE_FREQ': 10, 'EVAL_FREQ': 5, 'VIS_FREQ': 1, 'VIS_STEP_FREQ': 500}, 'LOG': {'LEVEL': 'INFO', 'SAVE_DIR': './logs'}, 'DEVICE': 'cuda', 'MIXED_PRECISION': True, 'SEED': 42}
2025-06-11 16:23:48,377 - train - INFO - Creating data loaders...
2025-06-11 16:23:51,097 - train - INFO - Training dataset: {'total_samples': 118396, 'total_videos': 593, 'total_annotations': 118396, 'total_tracks': 36, 'avg_frames_per_video': np.float64(199.65598650927487), 'max_frames_per_video': np.int64(837), 'min_frames_per_video': np.int64(33)}
2025-06-11 16:23:51,097 - train - INFO - Validation dataset: {'total_samples': 58489, 'total_videos': 170, 'total_annotations': 58489, 'total_tracks': 80, 'avg_frames_per_video': np.float64(344.0529411764706), 'max_frames_per_video': np.int64(1441), 'min_frames_per_video': np.int64(55)}
2025-06-11 16:23:51,097 - train - INFO - Creating model and optimizer...
2025-06-11 16:23:51,976 - train - INFO - Starting training...
2025-06-11 16:23:52,769 - train - INFO - Epoch 0, Batch 0/14799, Loss: 79.1942
2025-06-11 16:23:57,345 - train - INFO - Epoch 0, Batch 100/14799, Loss: 12.0752
2025-06-11 16:24:01,973 - train - INFO - Epoch 0, Batch 200/14799, Loss: 4.9918
2025-06-11 16:24:06,565 - train - INFO - Epoch 0, Batch 300/14799, Loss: 2.1365
2025-06-11 16:24:11,184 - train - INFO - Epoch 0, Batch 400/14799, Loss: 1.3134
2025-06-11 16:24:15,736 - train - INFO - Generating visualization at step 500...
2025-06-11 16:24:16,328 - train - INFO - Step visualization saved: ./outputs/posetrack_20250611_162348/step_visualizations/step_000500.png
2025-06-11 16:24:16,376 - train - INFO - Epoch 0, Batch 500/14799, Loss: -0.0766
2025-06-11 16:24:21,191 - train - INFO - Epoch 0, Batch 600/14799, Loss: -1.1112
2025-06-11 16:24:25,806 - train - INFO - Epoch 0, Batch 700/14799, Loss: -0.8803
2025-06-11 16:24:30,346 - train - INFO - Epoch 0, Batch 800/14799, Loss: -1.4848
2025-06-11 16:24:34,979 - train - INFO - Epoch 0, Batch 900/14799, Loss: -1.8817
2025-06-11 16:24:39,510 - train - INFO - Generating visualization at step 1000...
2025-06-11 16:24:39,948 - train - INFO - Step visualization saved: ./outputs/posetrack_20250611_162348/step_visualizations/step_001000.png
2025-06-11 16:24:39,996 - train - INFO - Epoch 0, Batch 1000/14799, Loss: -0.8912
2025-06-11 16:24:44,682 - train - INFO - Epoch 0, Batch 1100/14799, Loss: -2.4299
2025-06-11 16:24:49,256 - train - INFO - Epoch 0, Batch 1200/14799, Loss: -3.2550
2025-06-11 16:24:53,864 - train - INFO - Epoch 0, Batch 1300/14799, Loss: -3.9823
2025-06-11 16:24:58,436 - train - INFO - Epoch 0, Batch 1400/14799, Loss: -6.1725
2025-06-11 16:25:02,905 - train - INFO - Generating visualization at step 1500...
2025-06-11 16:25:03,305 - train - INFO - Step visualization saved: ./outputs/posetrack_20250611_162348/step_visualizations/step_001500.png
2025-06-11 16:25:03,353 - train - INFO - Epoch 0, Batch 1500/14799, Loss: -6.4487
2025-06-11 16:25:08,001 - train - INFO - Epoch 0, Batch 1600/14799, Loss: -6.9339
2025-06-11 16:25:12,599 - train - INFO - Epoch 0, Batch 1700/14799, Loss: -6.8336
2025-06-11 16:25:17,275 - train - INFO - Epoch 0, Batch 1800/14799, Loss: -8.5928
2025-06-11 16:25:21,814 - train - INFO - Epoch 0, Batch 1900/14799, Loss: -8.6322
2025-06-11 16:25:26,324 - train - INFO - Generating visualization at step 2000...
2025-06-11 16:25:26,734 - train - INFO - Step visualization saved: ./outputs/posetrack_20250611_162348/step_visualizations/step_002000.png
2025-06-11 16:25:26,781 - train - INFO - Epoch 0, Batch 2000/14799, Loss: -7.9015
2025-06-11 16:25:31,366 - train - INFO - Epoch 0, Batch 2100/14799, Loss: -8.5065
2025-06-11 16:25:35,897 - train - INFO - Epoch 0, Batch 2200/14799, Loss: -9.6943
2025-06-11 16:25:40,439 - train - INFO - Epoch 0, Batch 2300/14799, Loss: -9.1260
2025-06-11 16:25:45,086 - train - INFO - Epoch 0, Batch 2400/14799, Loss: -9.4877
2025-06-11 16:25:49,619 - train - INFO - Generating visualization at step 2500...
2025-06-11 16:25:50,108 - train - INFO - Step visualization saved: ./outputs/posetrack_20250611_162348/step_visualizations/step_002500.png
2025-06-11 16:25:50,154 - train - INFO - Epoch 0, Batch 2500/14799, Loss: -7.1753
2025-06-11 16:25:54,847 - train - INFO - Epoch 0, Batch 2600/14799, Loss: -9.4925
2025-06-11 16:25:59,459 - train - INFO - Epoch 0, Batch 2700/14799, Loss: -7.9752
2025-06-11 16:26:04,070 - train - INFO - Epoch 0, Batch 2800/14799, Loss: -9.1269
2025-06-11 16:26:08,869 - train - INFO - Epoch 0, Batch 2900/14799, Loss: -9.7938
2025-06-11 16:26:13,389 - train - INFO - Generating visualization at step 3000...
2025-06-11 16:26:13,904 - train - INFO - Step visualization saved: ./outputs/posetrack_20250611_162348/step_visualizations/step_003000.png
2025-06-11 16:26:13,951 - train - INFO - Epoch 0, Batch 3000/14799, Loss: -10.0333
2025-06-11 16:26:18,605 - train - INFO - Epoch 0, Batch 3100/14799, Loss: -9.5077
2025-06-11 16:26:23,285 - train - INFO - Epoch 0, Batch 3200/14799, Loss: -9.5276
2025-06-11 16:26:27,843 - train - INFO - Epoch 0, Batch 3300/14799, Loss: -9.8620
2025-06-11 16:26:32,396 - train - INFO - Epoch 0, Batch 3400/14799, Loss: -10.3992
2025-06-11 16:26:36,899 - train - INFO - Generating visualization at step 3500...
2025-06-11 16:26:37,296 - train - INFO - Step visualization saved: ./outputs/posetrack_20250611_162348/step_visualizations/step_003500.png
2025-06-11 16:26:37,343 - train - INFO - Epoch 0, Batch 3500/14799, Loss: -10.3144
2025-06-11 16:26:41,964 - train - INFO - Epoch 0, Batch 3600/14799, Loss: -10.9341
2025-06-11 16:26:46,589 - train - INFO - Epoch 0, Batch 3700/14799, Loss: -10.5480
2025-06-11 16:26:51,199 - train - INFO - Epoch 0, Batch 3800/14799, Loss: -10.4227
2025-06-11 16:26:55,801 - train - INFO - Epoch 0, Batch 3900/14799, Loss: -10.2820
2025-06-11 16:27:00,437 - train - INFO - Generating visualization at step 4000...
2025-06-11 16:27:00,857 - train - INFO - Step visualization saved: ./outputs/posetrack_20250611_162348/step_visualizations/step_004000.png
2025-06-11 16:27:00,906 - train - INFO - Epoch 0, Batch 4000/14799, Loss: -10.6003
2025-06-11 16:27:05,623 - train - INFO - Epoch 0, Batch 4100/14799, Loss: -11.0153
2025-06-11 16:27:10,263 - train - INFO - Epoch 0, Batch 4200/14799, Loss: -8.8534
2025-06-11 16:27:14,869 - train - INFO - Epoch 0, Batch 4300/14799, Loss: -10.0246
2025-06-11 16:27:19,443 - train - INFO - Epoch 0, Batch 4400/14799, Loss: -10.1284
2025-06-11 16:27:23,940 - train - INFO - Generating visualization at step 4500...
2025-06-11 16:27:24,384 - train - INFO - Step visualization saved: ./outputs/posetrack_20250611_162348/step_visualizations/step_004500.png
2025-06-11 16:27:24,431 - train - INFO - Epoch 0, Batch 4500/14799, Loss: -10.7706
2025-06-11 16:27:29,004 - train - INFO - Epoch 0, Batch 4600/14799, Loss: -10.6803
2025-06-11 16:27:33,514 - train - INFO - Epoch 0, Batch 4700/14799, Loss: -10.8747
