2025-06-11 16:08:24,915 - train - INFO - Starting training with config: {'MODEL': {'NUM_JOINTS': 17, 'IMAGE_SIZE': [256, 256], 'FC_DIM': [1024, 512], 'BACKBONE_LAYERS': 50, 'BACKBONE_PRETRAINED': True, 'SKELETON': [[15, 13], [13, 11], [16, 14], [14, 12], [11, 12], [5, 11], [6, 12], [5, 6], [5, 7], [6, 8], [7, 9], [8, 10], [1, 2], [0, 1], [0, 2], [1, 3], [2, 4], [3, 5], [4, 6]]}, 'DATASET': {'ROOT': './datasets/PoseTrack21/data', 'TRAIN_SET': 'train', 'VAL_SET': 'val', 'TEST_SET': 'test', 'FLIP': True, 'SCALE_FACTOR': 0.3, 'ROT_FACTOR': 40, 'COLOR_RGB': True}, 'TRAIN': {'BATCH_SIZE': 4, 'NUM_WORKERS': 8, 'EPOCHS': 1, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'LR_SCHEDULE': 'cosine', 'WARMUP_EPOCHS': 5, 'KEYPOINT_WEIGHT': 1.0, 'SPATIAL_WEIGHT': 1.0, 'TEMPORAL_WEIGHT': 1.0}, 'VAL': {'BATCH_SIZE': 32, 'FLIP_TEST': True}, 'INFERENCE': {'NMS_THRESHOLD': 0.5, 'KEYPOINT_THRESHOLD': 0.3, 'SPATIAL_THRESHOLD': 0.3, 'TEMPORAL_THRESHOLD': 0.3}, 'TRACKING': {'MAX_DISTANCE': 50, 'MIN_CONFIDENCE': 0.3, 'MAX_MISSING_FRAMES': 5}, 'OUTPUT': {'DIR': './outputs/posetrack_20250611_160824', 'SAVE_FREQ': 10, 'EVAL_FREQ': 5, 'VIS_FREQ': 1, 'VIS_STEP_FREQ': 500}, 'LOG': {'LEVEL': 'INFO', 'SAVE_DIR': './logs'}, 'DEVICE': 'cuda', 'MIXED_PRECISION': True, 'SEED': 42}
2025-06-11 16:08:24,916 - train - INFO - Creating data loaders...
2025-06-11 16:08:27,671 - train - INFO - Training dataset: {'total_samples': 118396, 'total_videos': 593, 'total_annotations': 118396, 'total_tracks': 36, 'avg_frames_per_video': np.float64(199.65598650927487), 'max_frames_per_video': np.int64(837), 'min_frames_per_video': np.int64(33)}
2025-06-11 16:08:27,671 - train - INFO - Validation dataset: {'total_samples': 58489, 'total_videos': 170, 'total_annotations': 58489, 'total_tracks': 80, 'avg_frames_per_video': np.float64(344.0529411764706), 'max_frames_per_video': np.int64(1441), 'min_frames_per_video': np.int64(55)}
2025-06-11 16:08:27,671 - train - INFO - Creating model and optimizer...
2025-06-11 16:08:28,555 - train - INFO - Starting training...
2025-06-11 16:08:29,279 - train - INFO - Epoch 0, Batch 0/29599, Loss: 27.2261
2025-06-11 16:08:33,731 - train - INFO - Epoch 0, Batch 100/29599, Loss: 5.0563
2025-06-11 16:08:38,223 - train - INFO - Epoch 0, Batch 200/29599, Loss: -2.1420
2025-06-11 16:08:42,712 - train - INFO - Epoch 0, Batch 300/29599, Loss: -5.5110
2025-06-11 16:08:47,232 - train - INFO - Epoch 0, Batch 400/29599, Loss: -7.4893
2025-06-11 16:08:51,684 - train - INFO - Generating visualization at step 500...
2025-06-11 16:08:52,838 - train - INFO - Step visualization saved: ./outputs/posetrack_20250611_160824/step_visualizations/step_000500.png
2025-06-11 16:08:52,885 - train - INFO - Epoch 0, Batch 500/29599, Loss: -5.9121
2025-06-11 16:08:57,460 - train - INFO - Epoch 0, Batch 600/29599, Loss: -8.4638
2025-06-11 16:09:02,047 - train - INFO - Epoch 0, Batch 700/29599, Loss: -7.8689
2025-06-11 16:09:06,545 - train - INFO - Epoch 0, Batch 800/29599, Loss: -8.1404
2025-06-11 16:09:11,034 - train - INFO - Epoch 0, Batch 900/29599, Loss: -8.2721
2025-06-11 16:09:15,529 - train - INFO - Generating visualization at step 1000...
2025-06-11 16:09:16,288 - train - INFO - Step visualization saved: ./outputs/posetrack_20250611_160824/step_visualizations/step_001000.png
2025-06-11 16:09:16,336 - train - INFO - Epoch 0, Batch 1000/29599, Loss: -8.6021
2025-06-11 16:09:20,915 - train - INFO - Epoch 0, Batch 1100/29599, Loss: -8.4765
2025-06-11 16:09:25,474 - train - INFO - Epoch 0, Batch 1200/29599, Loss: -9.2383
2025-06-11 16:09:30,001 - train - INFO - Epoch 0, Batch 1300/29599, Loss: -9.4902
2025-06-11 16:09:34,521 - train - INFO - Epoch 0, Batch 1400/29599, Loss: -9.5648
2025-06-11 16:09:38,996 - train - INFO - Generating visualization at step 1500...
2025-06-11 16:09:39,739 - train - INFO - Step visualization saved: ./outputs/posetrack_20250611_160824/step_visualizations/step_001500.png
2025-06-11 16:09:39,784 - train - INFO - Epoch 0, Batch 1500/29599, Loss: -9.4940
2025-06-11 16:09:44,345 - train - INFO - Epoch 0, Batch 1600/29599, Loss: -9.8515
2025-06-11 16:09:48,839 - train - INFO - Epoch 0, Batch 1700/29599, Loss: -10.8289
2025-06-11 16:09:53,381 - train - INFO - Epoch 0, Batch 1800/29599, Loss: -11.5256
2025-06-11 16:09:57,930 - train - INFO - Epoch 0, Batch 1900/29599, Loss: -12.7635
2025-06-11 16:10:02,401 - train - INFO - Generating visualization at step 2000...
2025-06-11 16:10:03,189 - train - INFO - Step visualization saved: ./outputs/posetrack_20250611_160824/step_visualizations/step_002000.png
2025-06-11 16:10:03,235 - train - INFO - Epoch 0, Batch 2000/29599, Loss: -13.1123
2025-06-11 16:10:07,756 - train - INFO - Epoch 0, Batch 2100/29599, Loss: -14.1363
2025-06-11 16:10:12,238 - train - INFO - Epoch 0, Batch 2200/29599, Loss: -14.2751
2025-06-11 16:10:16,786 - train - INFO - Epoch 0, Batch 2300/29599, Loss: -14.2784
2025-06-11 16:10:21,449 - train - INFO - Epoch 0, Batch 2400/29599, Loss: -14.7712
