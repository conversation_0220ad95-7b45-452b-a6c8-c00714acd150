2025-06-11 13:48:02,198 - train - INFO - Starting training with config: {'MODEL': {'NUM_JOINTS': 17, 'IMAGE_SIZE': [256, 256], 'FC_DIM': [1024, 512], 'BACKBONE_LAYERS': 50, 'BACKBONE_PRETRAINED': True, 'SKELETON': [[15, 13], [13, 11], [16, 14], [14, 12], [11, 12], [5, 11], [6, 12], [5, 6], [5, 7], [6, 8], [7, 9], [8, 10], [1, 2], [0, 1], [0, 2], [1, 3], [2, 4], [3, 5], [4, 6]]}, 'DATASET': {'ROOT': './datasets/PoseTrack21/data', 'TRAIN_SET': 'train', 'VAL_SET': 'val', 'TEST_SET': 'test', 'FLIP': True, 'SCALE_FACTOR': 0.3, 'ROT_FACTOR': 40, 'COLOR_RGB': True}, 'TRAIN': {'BATCH_SIZE': 2, 'NUM_WORKERS': 2, 'EPOCHS': 2, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'LR_SCHEDULE': 'cosine', 'WARMUP_EPOCHS': 5, 'KEYPOINT_WEIGHT': 1.0, 'SPATIAL_WEIGHT': 1.0, 'TEMPORAL_WEIGHT': 1.0}, 'VAL': {'BATCH_SIZE': 32, 'FLIP_TEST': True}, 'INFERENCE': {'NMS_THRESHOLD': 0.5, 'KEYPOINT_THRESHOLD': 0.3, 'SPATIAL_THRESHOLD': 0.3, 'TEMPORAL_THRESHOLD': 0.3}, 'TRACKING': {'MAX_DISTANCE': 50, 'MIN_CONFIDENCE': 0.3, 'MAX_MISSING_FRAMES': 5}, 'OUTPUT': {'DIR': './outputs/debug_test/posetrack_20250611_134802', 'SAVE_FREQ': 1, 'EVAL_FREQ': 1, 'VIS_FREQ': 20}, 'LOG': {'LEVEL': 'INFO', 'SAVE_DIR': './logs'}, 'DEVICE': 'cuda', 'MIXED_PRECISION': True, 'SEED': 42}
2025-06-11 13:48:02,199 - train - INFO - Creating data loaders...
2025-06-11 13:48:04,934 - train - INFO - Training dataset: {'total_samples': 118396, 'total_videos': 593, 'total_annotations': 118396, 'total_tracks': 36, 'avg_frames_per_video': np.float64(199.65598650927487), 'max_frames_per_video': np.int64(837), 'min_frames_per_video': np.int64(33)}
2025-06-11 13:48:04,935 - train - INFO - Validation dataset: {'total_samples': 58489, 'total_videos': 170, 'total_annotations': 58489, 'total_tracks': 80, 'avg_frames_per_video': np.float64(344.0529411764706), 'max_frames_per_video': np.int64(1441), 'min_frames_per_video': np.int64(55)}
2025-06-11 13:48:04,935 - train - INFO - Creating model and optimizer...
2025-06-11 13:48:05,311 - train - INFO - Starting training...
