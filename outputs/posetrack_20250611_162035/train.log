2025-06-11 16:20:35,532 - train - INFO - Starting training with config: {'MODEL': {'NUM_JOINTS': 17, 'IMAGE_SIZE': [256, 256], 'FC_DIM': [1024, 512], 'BACKBONE_LAYERS': 50, 'BACKBONE_PRETRAINED': True, 'SKELETON': [[15, 13], [13, 11], [16, 14], [14, 12], [11, 12], [5, 11], [6, 12], [5, 6], [5, 7], [6, 8], [7, 9], [8, 10], [1, 2], [0, 1], [0, 2], [1, 3], [2, 4], [3, 5], [4, 6]]}, 'DATASET': {'ROOT': './datasets/PoseTrack21/data', 'TRAIN_SET': 'train', 'VAL_SET': 'val', 'TEST_SET': 'test', 'FLIP': True, 'SCALE_FACTOR': 0.3, 'ROT_FACTOR': 40, 'COLOR_RGB': True}, 'TRAIN': {'BATCH_SIZE': 4, 'NUM_WORKERS': 8, 'EPOCHS': 1, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'LR_SCHEDULE': 'cosine', 'WARMUP_EPOCHS': 5, 'KEYPOINT_WEIGHT': 1.0, 'SPATIAL_WEIGHT': 1.0, 'TEMPORAL_WEIGHT': 1.0}, 'VAL': {'BATCH_SIZE': 32, 'FLIP_TEST': True}, 'INFERENCE': {'NMS_THRESHOLD': 0.5, 'KEYPOINT_THRESHOLD': 0.3, 'SPATIAL_THRESHOLD': 0.3, 'TEMPORAL_THRESHOLD': 0.3}, 'TRACKING': {'MAX_DISTANCE': 50, 'MIN_CONFIDENCE': 0.3, 'MAX_MISSING_FRAMES': 5}, 'OUTPUT': {'DIR': './outputs/posetrack_20250611_162035', 'SAVE_FREQ': 10, 'EVAL_FREQ': 5, 'VIS_FREQ': 1, 'VIS_STEP_FREQ': 500}, 'LOG': {'LEVEL': 'INFO', 'SAVE_DIR': './logs'}, 'DEVICE': 'cuda', 'MIXED_PRECISION': True, 'SEED': 42}
2025-06-11 16:20:35,533 - train - INFO - Creating data loaders...
2025-06-11 16:20:38,304 - train - INFO - Training dataset: {'total_samples': 118396, 'total_videos': 593, 'total_annotations': 118396, 'total_tracks': 36, 'avg_frames_per_video': np.float64(199.65598650927487), 'max_frames_per_video': np.int64(837), 'min_frames_per_video': np.int64(33)}
2025-06-11 16:20:38,304 - train - INFO - Validation dataset: {'total_samples': 58489, 'total_videos': 170, 'total_annotations': 58489, 'total_tracks': 80, 'avg_frames_per_video': np.float64(344.0529411764706), 'max_frames_per_video': np.int64(1441), 'min_frames_per_video': np.int64(55)}
2025-06-11 16:20:38,304 - train - INFO - Creating model and optimizer...
2025-06-11 16:20:39,198 - train - INFO - Starting training...
2025-06-11 16:20:39,891 - train - INFO - Epoch 0, Batch 0/29599, Loss: 73.2511
2025-06-11 16:20:44,262 - train - INFO - Epoch 0, Batch 100/29599, Loss: 9.1496
2025-06-11 16:20:48,750 - train - INFO - Epoch 0, Batch 200/29599, Loss: 5.6716
2025-06-11 16:20:53,271 - train - INFO - Epoch 0, Batch 300/29599, Loss: 3.1235
2025-06-11 16:20:57,726 - train - INFO - Epoch 0, Batch 400/29599, Loss: 0.4756
2025-06-11 16:21:02,155 - train - INFO - Generating visualization at step 500...
2025-06-11 16:21:02,786 - train - INFO - Step visualization saved: ./outputs/posetrack_20250611_162035/step_visualizations/step_000500.png
2025-06-11 16:21:02,835 - train - INFO - Epoch 0, Batch 500/29599, Loss: 0.6918
2025-06-11 16:21:07,574 - train - INFO - Epoch 0, Batch 600/29599, Loss: -0.2259
2025-06-11 16:21:12,066 - train - INFO - Epoch 0, Batch 700/29599, Loss: -0.3207
2025-06-11 16:21:16,593 - train - INFO - Epoch 0, Batch 800/29599, Loss: -0.5753
2025-06-11 16:21:21,128 - train - INFO - Epoch 0, Batch 900/29599, Loss: 0.1710
2025-06-11 16:21:25,591 - train - INFO - Generating visualization at step 1000...
2025-06-11 16:21:25,953 - train - INFO - Step visualization saved: ./outputs/posetrack_20250611_162035/step_visualizations/step_001000.png
2025-06-11 16:21:25,999 - train - INFO - Epoch 0, Batch 1000/29599, Loss: -0.3651
2025-06-11 16:21:30,503 - train - INFO - Epoch 0, Batch 1100/29599, Loss: -1.7406
2025-06-11 16:21:34,986 - train - INFO - Epoch 0, Batch 1200/29599, Loss: -2.5195
2025-06-11 16:21:39,508 - train - INFO - Epoch 0, Batch 1300/29599, Loss: -2.5440
2025-06-11 16:21:44,043 - train - INFO - Epoch 0, Batch 1400/29599, Loss: -1.7051
2025-06-11 16:21:48,561 - train - INFO - Generating visualization at step 1500...
2025-06-11 16:21:49,003 - train - INFO - Step visualization saved: ./outputs/posetrack_20250611_162035/step_visualizations/step_001500.png
2025-06-11 16:21:49,047 - train - INFO - Epoch 0, Batch 1500/29599, Loss: -2.3810
2025-06-11 16:21:53,645 - train - INFO - Epoch 0, Batch 1600/29599, Loss: -2.8169
2025-06-11 16:21:58,137 - train - INFO - Epoch 0, Batch 1700/29599, Loss: -3.9508
2025-06-11 16:22:02,652 - train - INFO - Epoch 0, Batch 1800/29599, Loss: -4.3597
2025-06-11 16:22:07,193 - train - INFO - Epoch 0, Batch 1900/29599, Loss: -4.1963
2025-06-11 16:22:11,663 - train - INFO - Generating visualization at step 2000...
2025-06-11 16:22:12,087 - train - INFO - Step visualization saved: ./outputs/posetrack_20250611_162035/step_visualizations/step_002000.png
2025-06-11 16:22:12,132 - train - INFO - Epoch 0, Batch 2000/29599, Loss: -4.8716
2025-06-11 16:22:16,681 - train - INFO - Epoch 0, Batch 2100/29599, Loss: -5.2709
2025-06-11 16:22:21,212 - train - INFO - Epoch 0, Batch 2200/29599, Loss: -4.4073
2025-06-11 16:22:25,749 - train - INFO - Epoch 0, Batch 2300/29599, Loss: -5.4052
