"""
Default configuration for RLE Bottom-up Pose Estimation and Tracking
"""

import torch
from easydict import EasyDict as edict

# Configuration object
cfg = edict()

# Model configuration
cfg.MODEL = edict()
cfg.MODEL.NUM_JOINTS = 17  # PoseTrack21 has 17 keypoints
cfg.MODEL.IMAGE_SIZE = [256, 256]  # Input image size
cfg.MODEL.FC_DIM = [1024, 512]  # Fully connected layer dimensions
cfg.MODEL.BACKBONE_LAYERS = 50  # ResNet-50
cfg.MODEL.BACKBONE_PRETRAINED = True

# PoseTrack21 skeleton connections (1-indexed to 0-indexed)
cfg.MODEL.SKELETON = [
    [15, 13], [13, 11], [16, 14], [14, 12], [11, 12],  # legs
    [5, 11], [6, 12], [5, 6],  # torso
    [5, 7], [6, 8], [7, 9], [8, 10],  # arms
    [1, 2], [0, 1], [0, 2], [1, 3], [2, 4], [3, 5], [4, 6]  # head
]

# Dataset configuration
cfg.DATASET = edict()
cfg.DATASET.ROOT = './datasets/PoseTrack21/data'
cfg.DATASET.TRAIN_SET = 'train'
cfg.DATASET.VAL_SET = 'val'
cfg.DATASET.TEST_SET = 'test'

# Data augmentation
cfg.DATASET.FLIP = True
cfg.DATASET.SCALE_FACTOR = 0.3
cfg.DATASET.ROT_FACTOR = 40
cfg.DATASET.COLOR_RGB = True

# Training configuration
cfg.TRAIN = edict()
cfg.TRAIN.BATCH_SIZE = 16
cfg.TRAIN.NUM_WORKERS = 8
cfg.TRAIN.EPOCHS = 100
cfg.TRAIN.LR = 1e-4
cfg.TRAIN.WEIGHT_DECAY = 1e-4
cfg.TRAIN.LR_SCHEDULE = 'cosine'
cfg.TRAIN.WARMUP_EPOCHS = 5

# Loss weights
cfg.TRAIN.KEYPOINT_WEIGHT = 1.0
cfg.TRAIN.SPATIAL_WEIGHT = 1.0
cfg.TRAIN.TEMPORAL_WEIGHT = 1.0

# Validation and testing
cfg.VAL = edict()
cfg.VAL.BATCH_SIZE = 32
cfg.VAL.FLIP_TEST = True

# Inference configuration
cfg.INFERENCE = edict()
cfg.INFERENCE.NMS_THRESHOLD = 0.5
cfg.INFERENCE.KEYPOINT_THRESHOLD = 0.3
cfg.INFERENCE.SPATIAL_THRESHOLD = 0.3
cfg.INFERENCE.TEMPORAL_THRESHOLD = 0.3

# Tracking configuration
cfg.TRACKING = edict()
cfg.TRACKING.MAX_DISTANCE = 50  # Maximum distance for temporal matching
cfg.TRACKING.MIN_CONFIDENCE = 0.3
cfg.TRACKING.MAX_MISSING_FRAMES = 5

# Output configuration
cfg.OUTPUT = edict()
cfg.OUTPUT.DIR = './outputs'
cfg.OUTPUT.SAVE_FREQ = 10  # Save checkpoint every N epochs
cfg.OUTPUT.EVAL_FREQ = 5   # Evaluate every N epochs
cfg.OUTPUT.VIS_FREQ = 1    # Visualize every N epochs

# Logging
cfg.LOG = edict()
cfg.LOG.LEVEL = 'INFO'
cfg.LOG.SAVE_DIR = './logs'

# Device configuration
cfg.DEVICE = 'cuda' if torch.cuda.is_available() else 'cpu'
cfg.MIXED_PRECISION = True

# Random seed
cfg.SEED = 42


def get_config():
    """Get a copy of the default config"""
    import copy
    return copy.deepcopy(cfg)


def update_config(cfg, args):
    """Update config with command line arguments"""
    if hasattr(args, 'data_root') and args.data_root:
        cfg.DATASET.ROOT = args.data_root
    
    if hasattr(args, 'output_dir') and args.output_dir:
        cfg.OUTPUT.DIR = args.output_dir
    
    if hasattr(args, 'batch_size') and args.batch_size:
        cfg.TRAIN.BATCH_SIZE = args.batch_size
    
    if hasattr(args, 'num_workers') and args.num_workers:
        cfg.TRAIN.NUM_WORKERS = args.num_workers
    
    if hasattr(args, 'epochs') and args.epochs:
        cfg.TRAIN.EPOCHS = args.epochs
    
    if hasattr(args, 'lr') and args.lr:
        cfg.TRAIN.LR = args.lr
    
    if hasattr(args, 'weight_decay') and args.weight_decay:
        cfg.TRAIN.WEIGHT_DECAY = args.weight_decay
    
    if hasattr(args, 'lr_schedule') and args.lr_schedule:
        cfg.TRAIN.LR_SCHEDULE = args.lr_schedule
    
    if hasattr(args, 'warmup_epochs') and args.warmup_epochs:
        cfg.TRAIN.WARMUP_EPOCHS = args.warmup_epochs
    
    if hasattr(args, 'keypoint_weight') and args.keypoint_weight:
        cfg.TRAIN.KEYPOINT_WEIGHT = args.keypoint_weight
    
    if hasattr(args, 'spatial_weight') and args.spatial_weight:
        cfg.TRAIN.SPATIAL_WEIGHT = args.spatial_weight
    
    if hasattr(args, 'temporal_weight') and args.temporal_weight:
        cfg.TRAIN.TEMPORAL_WEIGHT = args.temporal_weight
    
    if hasattr(args, 'save_freq') and args.save_freq:
        cfg.OUTPUT.SAVE_FREQ = args.save_freq
    
    if hasattr(args, 'eval_freq') and args.eval_freq:
        cfg.OUTPUT.EVAL_FREQ = args.eval_freq
    
    if hasattr(args, 'mixed_precision') and args.mixed_precision:
        cfg.MIXED_PRECISION = args.mixed_precision
    
    if hasattr(args, 'backbone_pretrained') and args.backbone_pretrained:
        cfg.MODEL.BACKBONE_PRETRAINED = args.backbone_pretrained
    
    return cfg
