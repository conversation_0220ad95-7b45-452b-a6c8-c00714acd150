"""
Data augmentation and preprocessing transforms
"""

import cv2
import numpy as np
import torch
import torchvision.transforms as transforms
from PIL import Image


class Compose:
    """Compose multiple transforms"""
    
    def __init__(self, transforms):
        self.transforms = transforms
    
    def __call__(self, image, keypoints=None, **kwargs):
        for t in self.transforms:
            if keypoints is not None:
                image, keypoints = t(image, keypoints, **kwargs)
            else:
                image = t(image, **kwargs)
        return image, keypoints


class ToTensor:
    """Convert PIL Image or numpy array to tensor"""
    
    def __call__(self, image, keypoints=None, **kwargs):
        if isinstance(image, Image.Image):
            image = transforms.ToTensor()(image)
        elif isinstance(image, np.ndarray):
            image = torch.from_numpy(image.transpose(2, 0, 1)).float() / 255.0
        
        if keypoints is not None:
            keypoints = torch.from_numpy(keypoints).float()
        
        return image, keypoints


class Normalize:
    """Normalize image with ImageNet statistics"""
    
    def __init__(self, mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]):
        self.normalize = transforms.Normalize(mean=mean, std=std)
    
    def __call__(self, image, keypoints=None, **kwargs):
        image = self.normalize(image)
        return image, keypoints


class Resize:
    """Resize image and adjust keypoints accordingly"""
    
    def __init__(self, size):
        self.size = size if isinstance(size, (list, tuple)) else (size, size)
    
    def __call__(self, image, keypoints=None, **kwargs):
        if isinstance(image, Image.Image):
            orig_size = image.size  # (width, height)
            image = image.resize(self.size, Image.BILINEAR)
        elif isinstance(image, np.ndarray):
            orig_size = (image.shape[1], image.shape[0])  # (width, height)
            image = cv2.resize(image, self.size, interpolation=cv2.INTER_LINEAR)
        
        if keypoints is not None:
            # Adjust keypoint coordinates
            scale_x = self.size[0] / orig_size[0]
            scale_y = self.size[1] / orig_size[1]
            
            keypoints[:, 0] *= scale_x
            keypoints[:, 1] *= scale_y
        
        return image, keypoints


class RandomHorizontalFlip:
    """Random horizontal flip with probability p"""
    
    def __init__(self, p=0.5):
        self.p = p
    
    def __call__(self, image, keypoints=None, **kwargs):
        if np.random.random() < self.p:
            if isinstance(image, Image.Image):
                image = image.transpose(Image.FLIP_LEFT_RIGHT)
                width = image.size[0]
            elif isinstance(image, np.ndarray):
                image = cv2.flip(image, 1)
                width = image.shape[1]
            
            if keypoints is not None:
                # Flip keypoints horizontally
                keypoints[:, 0] = width - keypoints[:, 0]
                
                # Swap left-right keypoints for PoseTrack21
                # PoseTrack21 keypoint order: nose, head_bottom, head_top, left_ear, right_ear,
                # left_shoulder, right_shoulder, left_elbow, right_elbow, left_wrist, right_wrist,
                # left_hip, right_hip, left_knee, right_knee, left_ankle, right_ankle
                left_right_pairs = [
                    (3, 4),   # left_ear, right_ear
                    (5, 6),   # left_shoulder, right_shoulder
                    (7, 8),   # left_elbow, right_elbow
                    (9, 10),  # left_wrist, right_wrist
                    (11, 12), # left_hip, right_hip
                    (13, 14), # left_knee, right_knee
                    (15, 16)  # left_ankle, right_ankle
                ]
                
                for left_idx, right_idx in left_right_pairs:
                    keypoints[[left_idx, right_idx]] = keypoints[[right_idx, left_idx]]
        
        return image, keypoints


class RandomScale:
    """Random scaling with given scale factor"""
    
    def __init__(self, scale_factor=0.3):
        self.scale_factor = scale_factor
    
    def __call__(self, image, keypoints=None, **kwargs):
        scale = 1.0 + np.random.uniform(-self.scale_factor, self.scale_factor)
        
        if isinstance(image, Image.Image):
            orig_size = image.size
            new_size = (int(orig_size[0] * scale), int(orig_size[1] * scale))
            image = image.resize(new_size, Image.BILINEAR)
        elif isinstance(image, np.ndarray):
            orig_size = (image.shape[1], image.shape[0])
            new_size = (int(orig_size[0] * scale), int(orig_size[1] * scale))
            image = cv2.resize(image, new_size, interpolation=cv2.INTER_LINEAR)
        
        if keypoints is not None:
            keypoints = keypoints.astype(np.float32) * scale
        
        return image, keypoints


def get_transforms(cfg, is_train=True):
    """Get data transforms for training or validation"""

    transforms_list = []

    if is_train:
        # Data augmentation for training (before final resize)
        if cfg.DATASET.SCALE_FACTOR > 0:
            transforms_list.append(RandomScale(cfg.DATASET.SCALE_FACTOR))

        if cfg.DATASET.FLIP:
            transforms_list.append(RandomHorizontalFlip(p=0.5))

    # Always resize to target size at the end to ensure consistent dimensions
    transforms_list.append(Resize(cfg.MODEL.IMAGE_SIZE))

    # Convert to tensor and normalize
    transforms_list.append(ToTensor())
    transforms_list.append(Normalize())

    return Compose(transforms_list)
