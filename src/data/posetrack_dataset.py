"""
PoseTrack21 Dataset Loader for RLE Bottom-up Pose Estimation and Tracking

This dataset loader generates ground truth for:
1. Keypoint coordinates
2. Spatial connection vectors (between joints of same person)
3. Temporal connection vectors (between frames for tracking)
"""

import os
import json
import numpy as np
from PIL import Image
import torch
from torch.utils.data import Dataset
from collections import defaultdict

from .transforms import get_transforms


class PoseTrackDataset(Dataset):
    """
    PoseTrack21 dataset for bottom-up pose estimation and tracking
    """
    
    def __init__(self, cfg, split='train', transforms=None):
        """
        Args:
            cfg: Configuration object
            split: 'train', 'val', or 'test'
            transforms: Data transforms
        """
        self.cfg = cfg
        self.split = split
        self.transforms = transforms or get_transforms(cfg, is_train=(split == 'train'))
        
        self.data_root = cfg.DATASET.ROOT
        self.image_size = cfg.MODEL.IMAGE_SIZE
        self.num_joints = cfg.MODEL.NUM_JOINTS
        self.skeleton = cfg.MODEL.SKELETON
        
        # Load dataset annotations
        self.annotations = self._load_annotations()
        
        # Group annotations by video for temporal processing
        self.video_groups = self._group_by_video()
        
        # Create sample list for training
        self.samples = self._create_sample_list()
        
        print(f"Loaded {len(self.samples)} samples for {split} split")
    
    def _load_annotations(self):
        """Load PoseTrack21 annotations"""
        annotations = []
        
        # PoseTrack21 data structure
        data_dir = os.path.join(self.data_root, 'posetrack_data', self.split)
        
        if not os.path.exists(data_dir):
            raise FileNotFoundError(f"Data directory not found: {data_dir}")
        
        # Load all JSON annotation files
        for json_file in os.listdir(data_dir):
            if json_file.endswith('.json'):
                json_path = os.path.join(data_dir, json_file)
                
                with open(json_path, 'r') as f:
                    data = json.load(f)
                
                # Process annotations
                images = {img['id']: img for img in data['images']}
                
                for ann in data.get('annotations', []):
                    if ann['image_id'] in images:
                        image_info = images[ann['image_id']]
                        
                        # Skip if no keypoints
                        if 'keypoints' not in ann or len(ann['keypoints']) == 0:
                            continue
                        
                        # Parse keypoints (x, y, visibility format)
                        keypoints = np.array(ann['keypoints']).reshape(-1, 3)
                        
                        # Only keep visible keypoints (visibility > 0)
                        valid_keypoints = keypoints[:, 2] > 0
                        
                        if not np.any(valid_keypoints):
                            continue
                        
                        annotation = {
                            'image_path': os.path.join(self.data_root, image_info['file_name']),
                            'keypoints': keypoints[:, :2],  # (x, y) coordinates
                            'visibility': keypoints[:, 2],  # visibility flags
                            'bbox': ann.get('bbox', [0, 0, 100, 100]),
                            'track_id': ann.get('track_id', -1),
                            'image_id': ann['image_id'],
                            'vid_id': image_info.get('vid_id', ''),
                            'frame_id': image_info.get('frame_id', image_info['id'])
                        }
                        
                        annotations.append(annotation)
        
        return annotations
    
    def _group_by_video(self):
        """Group annotations by video ID for temporal processing"""
        video_groups = defaultdict(list)
        
        for ann in self.annotations:
            video_groups[ann['vid_id']].append(ann)
        
        # Sort by frame_id within each video
        for vid_id in video_groups:
            video_groups[vid_id].sort(key=lambda x: x['frame_id'])
        
        return dict(video_groups)
    
    def _create_sample_list(self):
        """Create list of training samples"""
        samples = []
        
        for vid_id, video_anns in self.video_groups.items():
            for i, ann in enumerate(video_anns):
                sample = {
                    'current_frame': ann,
                    'video_id': vid_id,
                    'frame_index': i,
                    'total_frames': len(video_anns)
                }
                
                # Add previous frame for temporal connection if available
                if i > 0:
                    sample['previous_frame'] = video_anns[i-1]
                
                samples.append(sample)
        
        return samples
    
    def __len__(self):
        return len(self.samples)
    
    def __getitem__(self, idx):
        """Get a training sample"""
        sample = self.samples[idx]
        current_frame = sample['current_frame']
        
        # Load current frame image
        image_path = current_frame['image_path']
        if not os.path.exists(image_path):
            # Fallback to a default image or skip
            print(f"Warning: Image not found: {image_path}")
            return self.__getitem__((idx + 1) % len(self.samples))
        
        image = Image.open(image_path).convert('RGB')
        
        # Get keypoints and generate ground truth
        keypoints = current_frame['keypoints']
        visibility = current_frame['visibility']
        
        # Generate spatial connection ground truth
        spatial_gt = self._generate_spatial_connections(keypoints, visibility)
        
        # Generate temporal connection ground truth
        temporal_gt = self._generate_temporal_connections(sample)
        
        # Apply transforms
        if self.transforms:
            image, keypoints = self.transforms(image, keypoints)

        # Prepare labels - check if keypoints is already a tensor
        if isinstance(keypoints, torch.Tensor):
            keypoint_gt = keypoints.float()
        else:
            keypoint_gt = torch.from_numpy(keypoints).float()

        # Normalize keypoints to [0, 1] range
        keypoint_gt[:, 0] /= self.image_size[1]  # width
        keypoint_gt[:, 1] /= self.image_size[0]  # height

        labels = {
            'keypoint_gt': keypoint_gt,
            'spatial_gt': torch.from_numpy(spatial_gt).float(),
            'temporal_gt': torch.from_numpy(temporal_gt).float(),
            'visibility': torch.from_numpy(visibility).float(),
            'track_id': current_frame['track_id'],
            'image_id': current_frame['image_id'],
            'image_path': image_path  # Add original image path for visualization
        }
        
        return image, labels

    def _generate_spatial_connections(self, keypoints, visibility):
        """
        Generate spatial connection vectors between joints of the same person

        Args:
            keypoints: Keypoint coordinates (J, 2)
            visibility: Visibility flags (J,)

        Returns:
            Spatial connection vectors (S, 2) where S is number of skeleton connections
        """
        spatial_connections = np.zeros((len(self.skeleton), 2), dtype=np.float32)

        for i, (joint1_idx, joint2_idx) in enumerate(self.skeleton):
            # Check if both joints are visible
            if visibility[joint1_idx] > 0 and visibility[joint2_idx] > 0:
                # Connection vector from joint1 to joint2
                connection_vec = keypoints[joint2_idx] - keypoints[joint1_idx]
                spatial_connections[i] = connection_vec
            # If joints are not visible, keep zero vector

        return spatial_connections

    def _generate_temporal_connections(self, sample):
        """
        Generate temporal connection vectors for tracking

        Args:
            sample: Sample dictionary containing current and previous frame info

        Returns:
            Temporal connection vectors (J, 2) - displacement from previous to current frame
        """
        temporal_connections = np.zeros((self.num_joints, 2), dtype=np.float32)

        if 'previous_frame' not in sample:
            # No previous frame available, return zero vectors
            return temporal_connections

        current_frame = sample['current_frame']
        previous_frame = sample['previous_frame']

        # Check if same track ID (same person)
        if (current_frame['track_id'] == previous_frame['track_id'] and
            current_frame['track_id'] != -1):

            current_kpts = current_frame['keypoints']
            previous_kpts = previous_frame['keypoints']
            current_vis = current_frame['visibility']
            previous_vis = previous_frame['visibility']

            for j in range(self.num_joints):
                # Only compute temporal connection if joint is visible in both frames
                if current_vis[j] > 0 and previous_vis[j] > 0:
                    # Displacement vector from previous to current frame
                    displacement = current_kpts[j] - previous_kpts[j]
                    temporal_connections[j] = displacement

        return temporal_connections

    def get_video_sequences(self, vid_id):
        """Get all frames for a specific video (useful for evaluation)"""
        if vid_id in self.video_groups:
            return self.video_groups[vid_id]
        return []

    def get_statistics(self):
        """Get dataset statistics"""
        stats = {
            'total_samples': len(self.samples),
            'total_videos': len(self.video_groups),
            'total_annotations': len(self.annotations)
        }

        # Count tracks
        track_ids = set()
        for ann in self.annotations:
            if ann['track_id'] != -1:
                track_ids.add(ann['track_id'])
        stats['total_tracks'] = len(track_ids)

        # Average frames per video
        frame_counts = [len(frames) for frames in self.video_groups.values()]
        stats['avg_frames_per_video'] = np.mean(frame_counts)
        stats['max_frames_per_video'] = np.max(frame_counts)
        stats['min_frames_per_video'] = np.min(frame_counts)

        return stats
