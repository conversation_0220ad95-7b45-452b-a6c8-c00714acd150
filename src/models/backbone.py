"""
Backbone networks for pose estimation
Supports ResNet and HRNet architectures
"""

import torch
import torch.nn as nn
import torchvision.models as models


class ResNetBackbone(nn.Module):
    """
    ResNet backbone for feature extraction
    """
    
    def __init__(self, num_layers=50, pretrained=True):
        """
        Args:
            num_layers: Number of layers (18, 34, 50, 101, 152)
            pretrained: Whether to use ImageNet pretrained weights
        """
        super(ResNetBackbone, self).__init__()
        
        assert num_layers in [18, 34, 50, 101, 152], f"Unsupported ResNet layers: {num_layers}"
        
        self.num_layers = num_layers
        self.feature_channels = {
            18: 512, 34: 512, 50: 2048, 101: 2048, 152: 2048
        }[num_layers]
        
        # Load pretrained ResNet
        if num_layers == 18:
            resnet = models.resnet18(pretrained=pretrained)
        elif num_layers == 34:
            resnet = models.resnet34(pretrained=pretrained)
        elif num_layers == 50:
            resnet = models.resnet50(pretrained=pretrained)
        elif num_layers == 101:
            resnet = models.resnet101(pretrained=pretrained)
        elif num_layers == 152:
            resnet = models.resnet152(pretrained=pretrained)
        
        # Remove the final fully connected layer and average pooling
        self.conv1 = resnet.conv1
        self.bn1 = resnet.bn1
        self.relu = resnet.relu
        self.maxpool = resnet.maxpool
        self.layer1 = resnet.layer1
        self.layer2 = resnet.layer2
        self.layer3 = resnet.layer3
        self.layer4 = resnet.layer4
        
        # Initialize weights
        self._initialize_weights()
    
    def _initialize_weights(self):
        """Initialize weights for newly added layers"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
            elif isinstance(m, (nn.BatchNorm2d, nn.GroupNorm)):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
    
    def forward(self, x):
        """
        Forward pass
        
        Args:
            x: Input tensor (B, 3, H, W)
            
        Returns:
            Feature tensor (B, C, H/32, W/32)
        """
        x = self.conv1(x)
        x = self.bn1(x)
        x = self.relu(x)
        x = self.maxpool(x)
        
        x = self.layer1(x)
        x = self.layer2(x)
        x = self.layer3(x)
        x = self.layer4(x)
        
        return x


class HRNetBackbone(nn.Module):
    """
    HRNet backbone for high-resolution feature extraction
    TODO: Implement HRNet if needed
    """
    
    def __init__(self, config):
        super(HRNetBackbone, self).__init__()
        raise NotImplementedError("HRNet backbone not implemented yet")
    
    def forward(self, x):
        raise NotImplementedError("HRNet backbone not implemented yet")
