"""
Evaluation metrics for pose estimation and tracking
"""

import numpy as np
import torch
from collections import defaultdict


def compute_pck(pred_keypoints, gt_keypoints, visibility, threshold=0.2):
    """
    Compute Percentage of Correct Keypoints (PCK)
    
    Args:
        pred_keypoints: Predicted keypoints (N, J, 2)
        gt_keypoints: Ground truth keypoints (N, J, 2)
        visibility: Visibility flags (N, J)
        threshold: PCK threshold (relative to head size)
        
    Returns:
        PCK score
    """
    if len(pred_keypoints) == 0:
        return 0.0
    
    # Convert to numpy if needed
    if isinstance(pred_keypoints, torch.Tensor):
        pred_keypoints = pred_keypoints.cpu().numpy()
    if isinstance(gt_keypoints, torch.Tensor):
        gt_keypoints = gt_keypoints.cpu().numpy()
    if isinstance(visibility, torch.Tensor):
        visibility = visibility.cpu().numpy()
    
    # Compute distances
    distances = np.linalg.norm(pred_keypoints - gt_keypoints, axis=2)
    
    # Compute head size (distance between head_top and head_bottom)
    # Assuming keypoint order: nose(0), head_bottom(1), head_top(2), ...
    head_sizes = np.linalg.norm(gt_keypoints[:, 2] - gt_keypoints[:, 1], axis=1)
    head_sizes = np.maximum(head_sizes, 1.0)  # Avoid division by zero
    
    # Normalize distances by head size
    normalized_distances = distances / head_sizes[:, np.newaxis]
    
    # Count correct keypoints (visible and within threshold)
    correct = (normalized_distances < threshold) & (visibility > 0)
    total_visible = np.sum(visibility > 0)
    
    if total_visible == 0:
        return 0.0
    
    pck = np.sum(correct) / total_visible
    return pck


def compute_ap(pred_keypoints, gt_keypoints, visibility, thresholds=None):
    """
    Compute Average Precision (AP) for pose estimation
    
    Args:
        pred_keypoints: Predicted keypoints (N, J, 2)
        gt_keypoints: Ground truth keypoints (N, J, 2)
        visibility: Visibility flags (N, J)
        thresholds: List of PCK thresholds
        
    Returns:
        AP score
    """
    if thresholds is None:
        thresholds = np.arange(0.05, 0.95, 0.05)
    
    aps = []
    for threshold in thresholds:
        pck = compute_pck(pred_keypoints, gt_keypoints, visibility, threshold)
        aps.append(pck)
    
    return np.mean(aps)


def compute_tracking_metrics(pred_tracks, gt_tracks):
    """
    Compute tracking metrics (MOTA, IDF1, etc.)
    
    Args:
        pred_tracks: Predicted tracks {track_id: [(frame, keypoints), ...]}
        gt_tracks: Ground truth tracks {track_id: [(frame, keypoints), ...]}
        
    Returns:
        Dictionary of tracking metrics
    """
    # This is a simplified implementation
    # For full MOTA/IDF1 computation, consider using motmetrics library
    
    metrics = {
        'mota': 0.0,
        'idf1': 0.0,
        'num_switches': 0,
        'num_fragmentations': 0
    }
    
    # TODO: Implement proper tracking metrics
    # This would require:
    # 1. Frame-by-frame association between predictions and ground truth
    # 2. Counting true positives, false positives, false negatives
    # 3. Tracking identity switches and fragmentations
    
    return metrics


def compute_metrics(predictions, ground_truth, cfg):
    """
    Compute comprehensive evaluation metrics
    
    Args:
        predictions: Model predictions
        ground_truth: Ground truth annotations
        cfg: Configuration object
        
    Returns:
        Dictionary of metrics
    """
    metrics = {}
    
    # Extract keypoints and visibility
    pred_keypoints = predictions.get('keypoint_coord', [])
    gt_keypoints = ground_truth.get('keypoint_gt', [])
    visibility = ground_truth.get('visibility', [])
    
    if len(pred_keypoints) > 0 and len(gt_keypoints) > 0:
        # Pose estimation metrics
        metrics['pck'] = compute_pck(pred_keypoints, gt_keypoints, visibility)
        metrics['ap'] = compute_ap(pred_keypoints, gt_keypoints, visibility)
        
        # Per-joint PCK
        for joint_idx in range(pred_keypoints.shape[1]):
            joint_pck = compute_pck(
                pred_keypoints[:, joint_idx:joint_idx+1], 
                gt_keypoints[:, joint_idx:joint_idx+1],
                visibility[:, joint_idx:joint_idx+1]
            )
            metrics[f'pck_joint_{joint_idx}'] = joint_pck
    
    # TODO: Add tracking metrics when track predictions are available
    
    return metrics
