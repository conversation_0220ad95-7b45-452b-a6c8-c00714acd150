"""
Visualization utilities for pose estimation and tracking
"""

import os
import cv2
import numpy as np
import matplotlib.pyplot as plt
import torch


# PoseTrack21 skeleton connections
SKELETON_CONNECTIONS = [
    [15, 13], [13, 11], [16, 14], [14, 12], [11, 12],  # legs
    [5, 11], [6, 12], [5, 6],  # torso
    [5, 7], [6, 8], [7, 9], [8, 10],  # arms
    [1, 2], [0, 1], [0, 2], [1, 3], [2, 4], [3, 5], [4, 6]  # head
]

# Colors for different body parts
COLORS = {
    'head': (255, 0, 0),      # Red
    'torso': (0, 255, 0),     # Green
    'arms': (0, 0, 255),      # Blue
    'legs': (255, 255, 0),    # Yellow
    'keypoint': (255, 255, 255)  # White
}

# Joint names for PoseTrack21
JOINT_NAMES = [
    'nose', 'head_bottom', 'head_top', 'left_ear', 'right_ear',
    'left_shoulder', 'right_shoulder', 'left_elbow', 'right_elbow',
    'left_wrist', 'right_wrist', 'left_hip', 'right_hip',
    'left_knee', 'right_knee', 'left_ankle', 'right_ankle'
]


def draw_keypoints(image, keypoints, visibility=None, color=(255, 255, 255), radius=3):
    """
    Draw keypoints on image
    
    Args:
        image: Input image (H, W, 3)
        keypoints: Keypoints array (J, 2)
        visibility: Visibility flags (J,)
        color: Color for keypoints
        radius: Radius of keypoint circles
        
    Returns:
        Image with keypoints drawn
    """
    image = image.copy()
    
    for i, (x, y) in enumerate(keypoints):
        if visibility is None or visibility[i] > 0:
            cv2.circle(image, (int(x), int(y)), radius, color, -1)
    
    return image


def draw_skeleton(image, keypoints, visibility=None, connections=None, color=(0, 255, 0), thickness=2):
    """
    Draw skeleton connections on image
    
    Args:
        image: Input image (H, W, 3)
        keypoints: Keypoints array (J, 2)
        visibility: Visibility flags (J,)
        connections: List of joint connections
        color: Color for skeleton lines
        thickness: Line thickness
        
    Returns:
        Image with skeleton drawn
    """
    image = image.copy()
    
    if connections is None:
        connections = SKELETON_CONNECTIONS
    
    for joint1_idx, joint2_idx in connections:
        if joint1_idx < len(keypoints) and joint2_idx < len(keypoints):
            # Check if both joints are visible
            if (visibility is None or 
                (visibility[joint1_idx] > 0 and visibility[joint2_idx] > 0)):
                
                pt1 = (int(keypoints[joint1_idx][0]), int(keypoints[joint1_idx][1]))
                pt2 = (int(keypoints[joint2_idx][0]), int(keypoints[joint2_idx][1]))
                
                cv2.line(image, pt1, pt2, color, thickness)
    
    return image


def draw_pose(image, keypoints, visibility=None, track_id=None, color=None):
    """
    Draw complete pose (keypoints + skeleton) on image
    
    Args:
        image: Input image (H, W, 3)
        keypoints: Keypoints array (J, 2)
        visibility: Visibility flags (J,)
        track_id: Track ID for labeling
        color: Color for pose (if None, use default)
        
    Returns:
        Image with pose drawn
    """
    if color is None:
        # Generate color based on track_id
        if track_id is not None:
            np.random.seed(track_id)
            color = tuple(np.random.randint(0, 255, 3).tolist())
        else:
            color = (0, 255, 0)
    
    # Draw skeleton
    image = draw_skeleton(image, keypoints, visibility, color=color)
    
    # Draw keypoints
    image = draw_keypoints(image, keypoints, visibility, color=(255, 255, 255))
    
    # Draw track ID if provided
    if track_id is not None and len(keypoints) > 0:
        # Use head position for text
        head_pos = keypoints[0]  # nose
        cv2.putText(image, f'ID:{track_id}', 
                   (int(head_pos[0]), int(head_pos[1]) - 10),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
    
    return image


def _get_processed_image(images):
    """
    Convert processed tensor images back to displayable format

    Args:
        images: Tensor images (B, 3, H, W) or (B, H, W, 3)

    Returns:
        Single image in RGB format (H, W, 3)
    """
    if isinstance(images, torch.Tensor):
        images = images.cpu().numpy()

    # Handle different image formats
    if images.ndim == 4 and images.shape[1] == 3:  # (B, 3, H, W)
        images = images.transpose(0, 2, 3, 1)  # (B, H, W, 3)

    # Take first image
    image = images[0]

    # Denormalize if needed (assuming ImageNet normalization)
    if image.max() <= 1.0:
        mean = np.array([0.485, 0.456, 0.406])
        std = np.array([0.229, 0.224, 0.225])
        image = image * std + mean
        image = np.clip(image * 255, 0, 255).astype(np.uint8)

    return image


def visualize_predictions(images, predictions, ground_truth=None, save_path=None, epoch=None, step=None):
    """
    Visualize model predictions with GT and predicted poses + track IDs

    Args:
        images: Input images (B, 3, H, W) or (B, H, W, 3)
        predictions: Model predictions
        ground_truth: Ground truth annotations (optional)
        save_path: Path to save visualization
        epoch: Current epoch number
        step: Current training step number

    Returns:
        Visualization image
    """
    # Use original image if available in ground_truth
    if ground_truth is not None and 'image_path' in ground_truth:
        # Load original image
        image_path = ground_truth['image_path'][0]  # Take first image
        original_image = cv2.imread(image_path)
        if original_image is not None:
            original_image = cv2.cvtColor(original_image, cv2.COLOR_BGR2RGB)
        else:
            # Fallback to processed image
            original_image = _get_processed_image(images)
    else:
        # Use processed image
        original_image = _get_processed_image(images)

    # Create side-by-side comparison (GT left, Prediction right)
    fig, axes = plt.subplots(1, 2, figsize=(16, 8))

    # Left: Ground Truth
    gt_image = original_image.copy()
    if ground_truth is not None and 'keypoint_gt' in ground_truth:
        gt_keypoints = ground_truth['keypoint_gt'][0].cpu().numpy()  # Take first sample
        gt_visibility = ground_truth.get('visibility', None)
        gt_track_id = ground_truth.get('track_id', [None])[0] if 'track_id' in ground_truth else None

        # Convert normalized coordinates back to image coordinates
        img_height, img_width = gt_image.shape[:2]
        gt_keypoints_scaled = gt_keypoints.copy()
        gt_keypoints_scaled[:, 0] *= img_width   # x coordinates
        gt_keypoints_scaled[:, 1] *= img_height  # y coordinates

        if gt_visibility is not None:
            visibility = gt_visibility[0].cpu().numpy()
        else:
            visibility = np.ones(len(gt_keypoints))

        # Draw GT pose in red
        gt_image = draw_pose(gt_image, gt_keypoints_scaled, visibility,
                           track_id=gt_track_id, color=(255, 0, 0))

    axes[0].imshow(gt_image)
    gt_title = 'Ground Truth'
    if epoch is not None:
        gt_title += f' (Epoch {epoch}'
        if step is not None:
            gt_title += f', Step {step}'
        gt_title += ')'
    elif step is not None:
        gt_title += f' (Step {step})'
    axes[0].set_title(gt_title, fontsize=14, color='red', fontweight='bold')
    axes[0].axis('off')

    # Right: Predictions
    pred_image = original_image.copy()
    if 'keypoint_coord' in predictions:
        pred_keypoints = predictions['keypoint_coord'][0].cpu().numpy()  # Take first sample
        pred_scores = predictions.get('keypoint_scores', None)

        # Convert normalized coordinates back to image coordinates
        img_height, img_width = pred_image.shape[:2]
        pred_keypoints_scaled = pred_keypoints.copy()
        pred_keypoints_scaled[:, 0] *= img_width   # x coordinates
        pred_keypoints_scaled[:, 1] *= img_height  # y coordinates

        if pred_scores is not None:
            scores = pred_scores[0].cpu().numpy().flatten()
            visibility = scores > 0.3

            # Add confidence text
            avg_conf = np.mean(scores[visibility]) if np.any(visibility) else 0.0
            cv2.putText(pred_image, f'Conf: {avg_conf:.2f}',
                       (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
        else:
            visibility = np.ones(len(pred_keypoints))

        # Draw predicted pose in green
        pred_image = draw_pose(pred_image, pred_keypoints_scaled, visibility,
                             track_id='Pred', color=(0, 255, 0))

    axes[1].imshow(pred_image)
    axes[1].set_title('Prediction', fontsize=14, color='green', fontweight='bold')
    axes[1].axis('off')

    # Add overall title
    title = 'GT vs Prediction Comparison'
    if epoch is not None:
        title += f' - Epoch {epoch}'
        if step is not None:
            title += f', Step {step}'
    elif step is not None:
        title += f' - Step {step}'

    fig.suptitle(title, fontsize=18, fontweight='bold')

    plt.tight_layout()

    if save_path:
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        plt.savefig(save_path, dpi=150, bbox_inches='tight', facecolor='white')
        plt.close()
        return save_path
    else:
        plt.show()

    return fig


def create_video_from_frames(frame_paths, output_path, fps=30):
    """
    Create video from frame images
    
    Args:
        frame_paths: List of frame image paths
        output_path: Output video path
        fps: Frames per second
    """
    if not frame_paths:
        return
    
    # Read first frame to get dimensions
    first_frame = cv2.imread(frame_paths[0])
    height, width, _ = first_frame.shape
    
    # Create video writer
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    for frame_path in frame_paths:
        frame = cv2.imread(frame_path)
        out.write(frame)
    
    out.release()
    print(f"Video saved to: {output_path}")
