"""
Checkpoint utilities for saving and loading model states
"""

import os
import torch


def save_checkpoint(state, is_best, save_dir, filename='checkpoint.pth'):
    """
    Save checkpoint
    
    Args:
        state: Dictionary containing model state and other info
        is_best: Whether this is the best model so far
        save_dir: Directory to save checkpoint
        filename: Checkpoint filename
        
    Returns:
        Path to saved checkpoint
    """
    os.makedirs(save_dir, exist_ok=True)
    
    # Save regular checkpoint
    checkpoint_path = os.path.join(save_dir, filename)
    torch.save(state, checkpoint_path)
    
    # Save best model
    if is_best:
        best_path = os.path.join(save_dir, 'best_model.pth')
        torch.save(state, best_path)
    
    return checkpoint_path


def load_checkpoint(checkpoint_path):
    """
    Load checkpoint
    
    Args:
        checkpoint_path: Path to checkpoint file
        
    Returns:
        Loaded checkpoint dictionary
    """
    if not os.path.exists(checkpoint_path):
        raise FileNotFoundError(f"Checkpoint not found: {checkpoint_path}")
    
    checkpoint = torch.load(checkpoint_path, map_location='cpu')
    return checkpoint


def load_model_weights(model, checkpoint_path, strict=True):
    """
    Load only model weights from checkpoint
    
    Args:
        model: Model instance
        checkpoint_path: Path to checkpoint file
        strict: Whether to strictly enforce that the keys match
        
    Returns:
        Model with loaded weights
    """
    checkpoint = load_checkpoint(checkpoint_path)
    
    if 'model_state_dict' in checkpoint:
        state_dict = checkpoint['model_state_dict']
    else:
        state_dict = checkpoint
    
    model.load_state_dict(state_dict, strict=strict)
    return model
