"""
RLE-based Multi-Person Pose Tracker

This tracker uses temporal connection vectors predicted by the RLE model
to associate poses across frames without explicit Re-ID features.
"""

import numpy as np
from collections import defaultdict, deque
from scipy.optimize import linear_sum_assignment


class Track:
    """Individual track for a person"""
    
    def __init__(self, track_id, initial_pose, frame_id):
        self.track_id = track_id
        self.poses = {frame_id: initial_pose}
        self.last_frame = frame_id
        self.missing_frames = 0
        self.total_score = initial_pose.get('total_score', 0.0)
        self.length = 1
    
    def update(self, pose, frame_id):
        """Update track with new pose"""
        self.poses[frame_id] = pose
        self.last_frame = frame_id
        self.missing_frames = 0
        self.total_score += pose.get('total_score', 0.0)
        self.length += 1
    
    def mark_missing(self):
        """Mark track as missing in current frame"""
        self.missing_frames += 1
    
    def get_last_pose(self):
        """Get the most recent pose"""
        return self.poses[self.last_frame]
    
    def is_active(self, max_missing_frames):
        """Check if track is still active"""
        return self.missing_frames <= max_missing_frames


class RLETracker:
    """
    RLE-based multi-person pose tracker
    """
    
    def __init__(self, cfg):
        """
        Args:
            cfg: Configuration object
        """
        self.cfg = cfg
        self.num_joints = cfg.MODEL.NUM_JOINTS
        self.max_distance = cfg.TRACKING.MAX_DISTANCE
        self.min_confidence = cfg.TRACKING.MIN_CONFIDENCE
        self.max_missing_frames = cfg.TRACKING.MAX_MISSING_FRAMES
        self.temporal_threshold = cfg.INFERENCE.TEMPORAL_THRESHOLD
        
        # Track management
        self.tracks = {}
        self.next_track_id = 1
        self.frame_id = 0
        
        # History for temporal connections
        self.pose_history = deque(maxlen=2)  # Keep last 2 frames
    
    def update(self, pose_instances, temporal_predictions=None):
        """
        Update tracker with new pose detections
        
        Args:
            pose_instances: List of pose instances from current frame
            temporal_predictions: Temporal connection predictions from model
                - temporal_coord: (B, J, 2) - displacement vectors
                - temporal_sigma: (B, J, 2) - uncertainties
                - temporal_scores: (B, J, 1) - confidence scores
                
        Returns:
            Updated pose instances with track IDs
        """
        self.frame_id += 1
        
        # Filter poses by confidence
        valid_poses = []
        for pose in pose_instances:
            total_score = np.mean(pose['scores'][pose['visibility'] > 0])
            if total_score > self.min_confidence:
                pose['total_score'] = total_score
                valid_poses.append(pose)
        
        if len(valid_poses) == 0:
            # No valid poses, mark all tracks as missing
            for track in self.tracks.values():
                track.mark_missing()
            self._cleanup_tracks()
            return []
        
        # Associate poses with existing tracks
        if len(self.tracks) > 0 and temporal_predictions is not None:
            matched_poses = self._associate_with_temporal_connections(
                valid_poses, temporal_predictions
            )
        else:
            matched_poses = self._associate_with_distance(valid_poses)
        
        # Update tracks and create new ones
        updated_poses = self._update_tracks(matched_poses, valid_poses)
        
        # Cleanup inactive tracks
        self._cleanup_tracks()
        
        # Store pose history
        self.pose_history.append({
            'frame_id': self.frame_id,
            'poses': updated_poses
        })
        
        return updated_poses
    
    def _associate_with_temporal_connections(self, poses, temporal_predictions):
        """
        Associate poses using temporal connection vectors from RLE model
        
        Args:
            poses: List of pose instances
            temporal_predictions: Temporal predictions from model
            
        Returns:
            List of (track_id, pose_idx) matches
        """
        if len(self.pose_history) == 0:
            return []
        
        # Get previous frame poses
        prev_frame = self.pose_history[-1]
        prev_poses = prev_frame['poses']
        
        if len(prev_poses) == 0:
            return []
        
        # Extract temporal predictions
        temporal_coord = temporal_predictions['temporal_coord'].cpu().numpy()  # (B, J, 2)
        temporal_scores = temporal_predictions['temporal_scores'].cpu().numpy()  # (B, J, 1)
        
        # Compute association costs
        cost_matrix = np.full((len(prev_poses), len(poses)), np.inf)
        
        for prev_idx, prev_pose in enumerate(prev_poses):
            if 'track_id' not in prev_pose:
                continue
            
            # Get representative keypoint (e.g., center of visible keypoints)
            prev_visible = prev_pose['visibility'] > 0
            if not np.any(prev_visible):
                continue
            
            prev_center = np.mean(prev_pose['keypoints'][prev_visible], axis=0)
            
            for curr_idx, curr_pose in enumerate(poses):
                curr_visible = curr_pose['visibility'] > 0
                if not np.any(curr_visible):
                    continue
                
                curr_center = np.mean(curr_pose['keypoints'][curr_visible], axis=0)
                
                # Compute cost using temporal connections
                cost = self._compute_temporal_cost(
                    prev_pose, curr_pose, prev_center, curr_center,
                    temporal_coord[0], temporal_scores[0]  # Use first batch element
                )
                
                cost_matrix[prev_idx, curr_idx] = cost
        
        # Solve assignment problem
        if np.all(np.isinf(cost_matrix)):
            return []
        
        row_indices, col_indices = linear_sum_assignment(cost_matrix)
        
        matches = []
        for row_idx, col_idx in zip(row_indices, col_indices):
            if cost_matrix[row_idx, col_idx] < self.max_distance:
                prev_pose = prev_poses[row_idx]
                if 'track_id' in prev_pose:
                    matches.append((prev_pose['track_id'], col_idx))
        
        return matches
    
    def _compute_temporal_cost(self, prev_pose, curr_pose, prev_center, curr_center,
                              temporal_coord, temporal_scores):
        """
        Compute association cost using temporal connections
        
        Args:
            prev_pose, curr_pose: Pose instances
            prev_center, curr_center: Center positions
            temporal_coord: Temporal displacement vectors (J, 2)
            temporal_scores: Temporal confidence scores (J, 1)
            
        Returns:
            Association cost
        """
        # Compute actual displacement
        actual_displacement = curr_center - prev_center
        
        # Get predicted displacement from temporal connections
        # Use average of visible joint predictions
        prev_visible = prev_pose['visibility'] > 0
        curr_visible = curr_pose['visibility'] > 0
        common_visible = prev_visible & curr_visible
        
        if not np.any(common_visible):
            return np.inf
        
        # Weighted average of temporal predictions
        weights = temporal_scores[common_visible].flatten()
        if np.sum(weights) == 0:
            return np.inf
        
        predicted_displacements = temporal_coord[common_visible]
        weighted_prediction = np.average(predicted_displacements, axis=0, weights=weights)
        
        # Compute cost as difference between actual and predicted displacement
        displacement_error = np.linalg.norm(actual_displacement - weighted_prediction)
        
        # Add confidence penalty
        confidence_penalty = 1.0 - np.mean(weights)
        
        total_cost = displacement_error + confidence_penalty * 10.0
        
        return total_cost
    
    def _associate_with_distance(self, poses):
        """
        Fallback association using simple distance matching
        
        Args:
            poses: List of pose instances
            
        Returns:
            List of (track_id, pose_idx) matches
        """
        if len(self.pose_history) == 0:
            return []
        
        prev_frame = self.pose_history[-1]
        prev_poses = prev_frame['poses']
        
        if len(prev_poses) == 0:
            return []
        
        # Compute distance matrix
        cost_matrix = np.full((len(prev_poses), len(poses)), np.inf)
        
        for prev_idx, prev_pose in enumerate(prev_poses):
            if 'track_id' not in prev_pose:
                continue
            
            prev_visible = prev_pose['visibility'] > 0
            if not np.any(prev_visible):
                continue
            
            prev_center = np.mean(prev_pose['keypoints'][prev_visible], axis=0)
            
            for curr_idx, curr_pose in enumerate(poses):
                curr_visible = curr_pose['visibility'] > 0
                if not np.any(curr_visible):
                    continue
                
                curr_center = np.mean(curr_pose['keypoints'][curr_visible], axis=0)
                distance = np.linalg.norm(curr_center - prev_center)
                cost_matrix[prev_idx, curr_idx] = distance
        
        # Solve assignment
        if np.all(np.isinf(cost_matrix)):
            return []
        
        row_indices, col_indices = linear_sum_assignment(cost_matrix)
        
        matches = []
        for row_idx, col_idx in zip(row_indices, col_indices):
            if cost_matrix[row_idx, col_idx] < self.max_distance:
                prev_pose = prev_poses[row_idx]
                if 'track_id' in prev_pose:
                    matches.append((prev_pose['track_id'], col_idx))
        
        return matches
    
    def _update_tracks(self, matches, poses):
        """
        Update existing tracks and create new ones
        
        Args:
            matches: List of (track_id, pose_idx) matches
            poses: List of pose instances
            
        Returns:
            Updated pose instances with track IDs
        """
        matched_pose_indices = set()
        updated_poses = []
        
        # Update existing tracks
        for track_id, pose_idx in matches:
            if track_id in self.tracks:
                pose = poses[pose_idx].copy()
                pose['track_id'] = track_id
                self.tracks[track_id].update(pose, self.frame_id)
                updated_poses.append(pose)
                matched_pose_indices.add(pose_idx)
        
        # Mark unmatched tracks as missing
        matched_track_ids = set(track_id for track_id, _ in matches)
        for track_id, track in self.tracks.items():
            if track_id not in matched_track_ids:
                track.mark_missing()
        
        # Create new tracks for unmatched poses
        for pose_idx, pose in enumerate(poses):
            if pose_idx not in matched_pose_indices:
                track_id = self.next_track_id
                self.next_track_id += 1
                
                pose = pose.copy()
                pose['track_id'] = track_id
                
                self.tracks[track_id] = Track(track_id, pose, self.frame_id)
                updated_poses.append(pose)
        
        return updated_poses
    
    def _cleanup_tracks(self):
        """Remove inactive tracks"""
        inactive_tracks = []
        
        for track_id, track in self.tracks.items():
            if not track.is_active(self.max_missing_frames):
                inactive_tracks.append(track_id)
        
        for track_id in inactive_tracks:
            del self.tracks[track_id]
    
    def get_active_tracks(self):
        """Get all currently active tracks"""
        return {track_id: track for track_id, track in self.tracks.items() 
                if track.is_active(self.max_missing_frames)}
    
    def reset(self):
        """Reset tracker state"""
        self.tracks = {}
        self.next_track_id = 1
        self.frame_id = 0
        self.pose_history.clear()
