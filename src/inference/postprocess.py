"""
Post-processing for RLE Bottom-up Pose Estimation

This module handles:
1. Keypoint candidate generation from network outputs
2. Spatial grouping using spatial connection vectors
3. Non-maximum suppression
"""

import numpy as np
import torch
from scipy.optimize import linear_sum_assignment
from collections import defaultdict


class PostProcessor:
    """
    Post-processor for RLE bottom-up pose estimation
    """
    
    def __init__(self, cfg):
        """
        Args:
            cfg: Configuration object
        """
        self.cfg = cfg
        self.num_joints = cfg.MODEL.NUM_JOINTS
        self.skeleton = cfg.MODEL.SKELETON
        self.keypoint_threshold = cfg.INFERENCE.KEYPOINT_THRESHOLD
        self.spatial_threshold = cfg.INFERENCE.SPATIAL_THRESHOLD
        self.nms_threshold = cfg.INFERENCE.NMS_THRESHOLD
    
    def process(self, predictions):
        """
        Process network predictions to get final pose instances
        
        Args:
            predictions: Model predictions containing:
                - keypoint_coord: (B, J, 2)
                - keypoint_sigma: (B, J, 2) 
                - keypoint_scores: (B, J, 1)
                - spatial_coord: (B, S, 2)
                - spatial_sigma: (B, S, 2)
                - spatial_scores: (B, S, 1)
                
        Returns:
            List of pose instances for each image in batch
        """
        batch_size = predictions['keypoint_coord'].shape[0]
        results = []
        
        for b in range(batch_size):
            # Extract predictions for this image
            keypoint_coord = predictions['keypoint_coord'][b].cpu().numpy()  # (J, 2)
            keypoint_scores = predictions['keypoint_scores'][b].cpu().numpy().flatten()  # (J,)
            spatial_coord = predictions['spatial_coord'][b].cpu().numpy()  # (S, 2)
            spatial_scores = predictions['spatial_scores'][b].cpu().numpy().flatten()  # (S,)
            
            # Generate keypoint candidates
            keypoint_candidates = self._generate_keypoint_candidates(
                keypoint_coord, keypoint_scores
            )
            
            # Perform spatial grouping
            pose_instances = self._spatial_grouping(
                keypoint_candidates, spatial_coord, spatial_scores
            )
            
            # Apply non-maximum suppression
            pose_instances = self._nms(pose_instances)
            
            results.append(pose_instances)
        
        return results
    
    def _generate_keypoint_candidates(self, keypoint_coord, keypoint_scores):
        """
        Generate keypoint candidates from network predictions
        
        Args:
            keypoint_coord: Keypoint coordinates (J, 2)
            keypoint_scores: Keypoint confidence scores (J,)
            
        Returns:
            List of keypoint candidates
        """
        candidates = []
        
        for joint_idx in range(self.num_joints):
            score = keypoint_scores[joint_idx]
            
            if score > self.keypoint_threshold:
                candidate = {
                    'joint_type': joint_idx,
                    'position': keypoint_coord[joint_idx],
                    'score': score,
                    'used': False
                }
                candidates.append(candidate)
        
        return candidates
    
    def _spatial_grouping(self, keypoint_candidates, spatial_coord, spatial_scores):
        """
        Group keypoints into pose instances using spatial connections
        
        Args:
            keypoint_candidates: List of keypoint candidates
            spatial_coord: Spatial connection vectors (S, 2)
            spatial_scores: Spatial connection scores (S,)
            
        Returns:
            List of pose instances
        """
        pose_instances = []
        
        # Group candidates by joint type
        candidates_by_joint = defaultdict(list)
        for candidate in keypoint_candidates:
            candidates_by_joint[candidate['joint_type']].append(candidate)
        
        # Start grouping from high-confidence keypoints
        all_candidates = sorted(keypoint_candidates, key=lambda x: x['score'], reverse=True)
        
        for seed_candidate in all_candidates:
            if seed_candidate['used']:
                continue
            
            # Start a new pose instance
            pose_instance = {
                'keypoints': np.zeros((self.num_joints, 2)),
                'scores': np.zeros(self.num_joints),
                'visibility': np.zeros(self.num_joints)
            }
            
            # Add seed keypoint
            joint_type = seed_candidate['joint_type']
            pose_instance['keypoints'][joint_type] = seed_candidate['position']
            pose_instance['scores'][joint_type] = seed_candidate['score']
            pose_instance['visibility'][joint_type] = 1
            seed_candidate['used'] = True
            
            # Grow the pose instance using spatial connections
            self._grow_pose_instance(
                pose_instance, candidates_by_joint, spatial_coord, spatial_scores
            )
            
            # Only keep pose instances with sufficient keypoints
            if np.sum(pose_instance['visibility']) >= 3:
                pose_instances.append(pose_instance)
        
        return pose_instances
    
    def _grow_pose_instance(self, pose_instance, candidates_by_joint, spatial_coord, spatial_scores):
        """
        Grow pose instance by adding connected keypoints
        
        Args:
            pose_instance: Current pose instance
            candidates_by_joint: Candidates grouped by joint type
            spatial_coord: Spatial connection vectors (S, 2)
            spatial_scores: Spatial connection scores (S,)
        """
        changed = True
        
        while changed:
            changed = False
            
            for conn_idx, (joint1_idx, joint2_idx) in enumerate(self.skeleton):
                if spatial_scores[conn_idx] < self.spatial_threshold:
                    continue
                
                # Check if we can extend from joint1 to joint2
                if (pose_instance['visibility'][joint1_idx] > 0 and 
                    pose_instance['visibility'][joint2_idx] == 0):
                    
                    # Predict joint2 position using spatial connection
                    predicted_pos = (pose_instance['keypoints'][joint1_idx] + 
                                   spatial_coord[conn_idx])
                    
                    # Find best matching candidate for joint2
                    best_candidate = self._find_best_candidate(
                        candidates_by_joint[joint2_idx], predicted_pos
                    )
                    
                    if best_candidate is not None:
                        pose_instance['keypoints'][joint2_idx] = best_candidate['position']
                        pose_instance['scores'][joint2_idx] = best_candidate['score']
                        pose_instance['visibility'][joint2_idx] = 1
                        best_candidate['used'] = True
                        changed = True
                
                # Check if we can extend from joint2 to joint1
                elif (pose_instance['visibility'][joint2_idx] > 0 and 
                      pose_instance['visibility'][joint1_idx] == 0):
                    
                    # Predict joint1 position using spatial connection
                    predicted_pos = (pose_instance['keypoints'][joint2_idx] - 
                                   spatial_coord[conn_idx])
                    
                    # Find best matching candidate for joint1
                    best_candidate = self._find_best_candidate(
                        candidates_by_joint[joint1_idx], predicted_pos
                    )
                    
                    if best_candidate is not None:
                        pose_instance['keypoints'][joint1_idx] = best_candidate['position']
                        pose_instance['scores'][joint1_idx] = best_candidate['score']
                        pose_instance['visibility'][joint1_idx] = 1
                        best_candidate['used'] = True
                        changed = True
    
    def _find_best_candidate(self, candidates, predicted_pos, max_distance=50):
        """
        Find best matching candidate near predicted position
        
        Args:
            candidates: List of candidate keypoints
            predicted_pos: Predicted position (2,)
            max_distance: Maximum distance threshold
            
        Returns:
            Best matching candidate or None
        """
        best_candidate = None
        best_distance = float('inf')
        
        for candidate in candidates:
            if candidate['used']:
                continue
            
            distance = np.linalg.norm(candidate['position'] - predicted_pos)
            
            if distance < max_distance and distance < best_distance:
                best_distance = distance
                best_candidate = candidate
        
        return best_candidate
    
    def _nms(self, pose_instances):
        """
        Apply non-maximum suppression to remove duplicate poses
        
        Args:
            pose_instances: List of pose instances
            
        Returns:
            Filtered pose instances
        """
        if len(pose_instances) <= 1:
            return pose_instances
        
        # Compute pairwise similarities
        similarities = np.zeros((len(pose_instances), len(pose_instances)))
        
        for i in range(len(pose_instances)):
            for j in range(i + 1, len(pose_instances)):
                sim = self._compute_pose_similarity(pose_instances[i], pose_instances[j])
                similarities[i, j] = sim
                similarities[j, i] = sim
        
        # Apply NMS
        keep = []
        scores = [np.mean(instance['scores'][instance['visibility'] > 0]) 
                 for instance in pose_instances]
        
        indices = np.argsort(scores)[::-1]  # Sort by score descending
        
        while len(indices) > 0:
            # Keep the highest scoring instance
            current_idx = indices[0]
            keep.append(current_idx)
            
            # Remove similar instances
            remaining = []
            for idx in indices[1:]:
                if similarities[current_idx, idx] < self.nms_threshold:
                    remaining.append(idx)
            
            indices = remaining
        
        return [pose_instances[i] for i in keep]
    
    def _compute_pose_similarity(self, pose1, pose2):
        """
        Compute similarity between two pose instances
        
        Args:
            pose1, pose2: Pose instances
            
        Returns:
            Similarity score (0-1)
        """
        # Find common visible keypoints
        common_visible = (pose1['visibility'] > 0) & (pose2['visibility'] > 0)
        
        if np.sum(common_visible) == 0:
            return 0.0
        
        # Compute average distance for common keypoints
        distances = np.linalg.norm(
            pose1['keypoints'][common_visible] - pose2['keypoints'][common_visible],
            axis=1
        )
        
        avg_distance = np.mean(distances)
        
        # Convert distance to similarity (closer = more similar)
        similarity = np.exp(-avg_distance / 50.0)  # Adjust scale as needed
        
        return similarity
