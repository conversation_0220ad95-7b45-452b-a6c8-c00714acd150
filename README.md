# PKTrack: RLE-based Bottom-up Multi-Person Pose Estimation and Tracking

PKTrack은 RLE(Residual Log-likelihood Estimation)를 활용하여 히트맵 없이 직접 키포인트 좌표와 연결 정보를 회귀하는 Bottom-up 방식의 다중 인물 자세 추정 및 트래킹 모델입니다.

## 🎯 주요 특징

- **Bottom-up 방식의 직접 회귀**: 히트맵 대신 키포인트 좌표를 직접 예측
- **RLE 기반 불확실성 추정**: 각 예측값의 불확실성을 모델링
- **통합된 트래킹**: 명시적인 Re-ID 손실 없이 시간적 연결을 학습
- **PoseTrack21 지원**: 17개 키포인트를 가진 PoseTrack21 데이터셋 지원

## 🏗️ 모델 구조

모델은 다음 세 가지 정보를 직접 회귀합니다:

1. **키포인트 좌표 (x, y)**: 각 관절 유형의 좌표
2. **공간적 연결 벡터**: 같은 사람 내의 관절 간 연결
3. **시간적 연결 벡터**: 프레임 간 트래킹을 위한 변위

각 예측값에 대해 RLE를 통한 불확실성 추정이 함께 수행됩니다.

## 📁 프로젝트 구조

```
pktrack/
├── config/                 # 설정 파일
│   ├── __init__.py
│   └── default.py          # 기본 설정
├── src/                    # 소스 코드
│   ├── models/             # 모델 구현
│   │   ├── __init__.py
│   │   ├── backbone.py     # ResNet 백본
│   │   ├── real_nvp.py     # RealNVP 구현
│   │   └── rle_bottomup.py # 메인 모델
│   ├── data/               # 데이터 로더
│   │   ├── __init__.py
│   │   ├── posetrack_dataset.py
│   │   └── transforms.py
│   ├── inference/          # 추론 및 후처리
│   │   ├── __init__.py
│   │   ├── postprocess.py  # 후처리
│   │   └── tracker.py      # RLE 트래커
│   ├── utils/              # 유틸리티
│   │   ├── __init__.py
│   │   ├── logger.py
│   │   ├── checkpoint.py
│   │   ├── metrics.py
│   │   └── visualization.py
│   └── train.py            # 학습 스크립트
├── datasets/               # 데이터셋
│   └── PoseTrack21/
├── demo.py                 # 데모 스크립트
├── RLE_pose.py            # 기존 RLE 구현 (참고용)
└── README.md
```

## 🚀 설치 및 설정

### 1. 환경 설정

```bash
# Python 3.8+ 권장
pip install torch torchvision torchaudio
pip install opencv-python pillow matplotlib scipy tqdm easydict
```

### 2. 데이터셋 준비

PoseTrack21 데이터셋을 다운로드하고 다음 구조로 배치:

```
datasets/PoseTrack21/data/
├── posetrack_data/
│   ├── train/
│   ├── val/
│   └── test/
└── images/
    ├── train/
    ├── val/
    └── test/
```

## 🏋️ 학습

### 기본 학습

```bash
python src/train.py \
    --data_root ./datasets/PoseTrack21/data \
    --output_dir ./outputs \
    --batch_size 16 \
    --epochs 100 \
    --lr 1e-4
```

### 고급 옵션

```bash
python src/train.py \
    --data_root ./datasets/PoseTrack21/data \
    --output_dir ./outputs \
    --batch_size 16 \
    --num_workers 8 \
    --epochs 100 \
    --lr 1e-4 \
    --weight_decay 1e-4 \
    --lr_schedule cosine \
    --warmup_epochs 5 \
    --keypoint_weight 1.0 \
    --spatial_weight 1.0 \
    --temporal_weight 1.0 \
    --save_freq 10 \
    --eval_freq 5 \
    --mixed_precision \
    --backbone_pretrained
```

### 학습 재개

```bash
python src/train.py \
    --resume ./outputs/posetrack_20240101_120000/checkpoint.pth \
    [other options...]
```

## 🎬 데모 실행

### 단일 이미지 처리

```bash
python demo.py \
    --input path/to/image.jpg \
    --checkpoint path/to/model.pth \
    --output ./demo_output
```

### 이미지 시퀀스 처리 (트래킹 포함)

```bash
python demo.py \
    --input path/to/image_directory \
    --checkpoint path/to/model.pth \
    --output ./demo_output \
    --save_video
```

### 비디오 처리

```bash
python demo.py \
    --input path/to/video.mp4 \
    --checkpoint path/to/model.pth \
    --output ./demo_output \
    --save_video \
    --fps 30
```

## 📊 평가 지표

모델은 다음 지표들로 평가됩니다:

- **자세 추정**: PCK (Percentage of Correct Keypoints), AP (Average Precision)
- **트래킹**: MOTA (Multiple Object Tracking Accuracy), IDF1 (Identity F1 Score)

## 🔧 설정 옵션

주요 설정 옵션들:

```python
# 모델 설정
cfg.MODEL.NUM_JOINTS = 17           # 키포인트 개수
cfg.MODEL.IMAGE_SIZE = [256, 256]   # 입력 이미지 크기
cfg.MODEL.BACKBONE_LAYERS = 50      # ResNet 레이어 수

# 학습 설정
cfg.TRAIN.BATCH_SIZE = 16           # 배치 크기
cfg.TRAIN.LR = 1e-4                 # 학습률
cfg.TRAIN.EPOCHS = 100              # 에포크 수

# 추론 설정
cfg.INFERENCE.KEYPOINT_THRESHOLD = 0.3    # 키포인트 임계값
cfg.INFERENCE.SPATIAL_THRESHOLD = 0.3     # 공간 연결 임계값
cfg.INFERENCE.TEMPORAL_THRESHOLD = 0.3    # 시간 연결 임계값

# 트래킹 설정
cfg.TRACKING.MAX_DISTANCE = 50            # 최대 매칭 거리
cfg.TRACKING.MAX_MISSING_FRAMES = 5       # 최대 누락 프레임
```

## 🧪 실험 및 결과

### 학습 과정 모니터링

학습 중에는 다음 정보들이 로그됩니다:

- 각 손실 함수별 값 (키포인트, 공간 연결, 시간 연결)
- 검증 성능 지표
- 학습 시간 및 메모리 사용량

### 시각화

- 매 N 에포크마다 예측 결과 시각화 저장
- 트래킹 결과를 비디오로 출력
- 손실 함수 그래프 및 성능 지표 차트

## 🤝 기여하기

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📄 라이선스

이 프로젝트는 MIT 라이선스 하에 배포됩니다. 자세한 내용은 `LICENSE` 파일을 참조하세요.

## 📚 참고 문헌

- RLE (Residual Log-likelihood Estimation) 논문
- PoseTrack21 데이터셋
- RealNVP: Real-valued Non-Volume Preserving Transformations

## 🙋‍♂️ 문의사항

프로젝트에 대한 질문이나 제안사항이 있으시면 이슈를 생성해 주세요.
